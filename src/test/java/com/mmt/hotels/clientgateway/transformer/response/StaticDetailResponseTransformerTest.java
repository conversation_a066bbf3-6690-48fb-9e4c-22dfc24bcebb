package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.response.staticdetail.HouseRulesV2;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.desktop.StaticDetailResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import com.mmt.hotels.model.response.staticdata.CommonRules;
import com.mmt.hotels.model.response.staticdata.Rule;
import com.mmt.hotels.model.response.staticdata.RuleInfo;
import com.mmt.hotels.pojo.FoodAndDining.FoodAndDiningEnums;
import com.mmt.model.util.RatingDetail;
import com.mmt.propertymanager.config.PropertyManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.model.UGCRatingData;
import com.mmt.hotels.clientgateway.response.staticdetail.Restaurant;
import com.mmt.hotels.clientgateway.response.HighlightedAmenity;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class StaticDetailResponseTransformerTest {

    @Mock
    ObjectMapperUtil objectMapperUtil;
    @Mock
    CommonResponseTransformer commonResponseTransformer;
    @Mock
    private PropertyManager propertyManager;
    @Mock
    private PolyglotService polyglotService;
    @Mock
    private PolyglotHelper polyglotHelper;
    @Mock
    private Utility utility;

    @InjectMocks
    private StaticDetailResponseTransformerDesktop staticDetailResponseTransformer;

    @Before
    public void init(){
        // Mock utility.getUrlFromConfig to return the same URL that was passed in
        when(utility.getUrlFromConfig(anyString())).thenAnswer(invocation -> invocation.getArgument(0));
        
        ReflectionTestUtils.setField(staticDetailResponseTransformer, "view360IconUrl", "www.sample360Url.com");
        ReflectionTestUtils.setField(staticDetailResponseTransformer, "foodDiningMergeSections", 
            Arrays.asList("/FOOD_AND_DINING/", "KITCHEN"));
    }

    @Test
    public void buildFoodDining_WhenFoodDiningRuleIsEmpty_ShouldReturnEmptyHouseRulesV2() {
        // Arrange
        List<CommonRules> foodDiningRule = new ArrayList<>();
        boolean foodDiningHighlight = true;
        StaticDetailCriteria staticDetailRequest = new StaticDetailCriteria();
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean foodAndDiningV2 = false;
        boolean foodDiningRevamp = true;
        UGCRatingData foodRatingData = null;
        boolean isAltAcco = false;
        boolean isDhCall = false;

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDining", foodDiningRule, foodDiningHighlight, staticDetailRequest,
                deviceDetails, foodAndDiningV2, foodDiningRevamp, foodRatingData, isAltAcco, isDhCall, true, false);

        // Assert
        assertNotNull(result);
        assertNull(result.getAllRules());
        assertNull(result.getSummary());
        assertNull(result.getSummaryV2());
        assertNull(result.getRestaurants());
    }

    @Test
    public void buildFoodDining_WhenFoodDiningRuleHasRestaurant_ShouldIncludeRestaurantDetails() {
        // Arrange
        List<CommonRules> foodDiningRule = new ArrayList<>();
        CommonRules restaurantRule = new CommonRules();
        restaurantRule.setCategory(FoodAndDiningEnums.Restaurant.getName());
        restaurantRule.setHostCatHeading("Restaurant Name");
        restaurantRule.setId("restaurant1");
        restaurantRule.setImages(Arrays.asList("image1.jpg"));
        restaurantRule.setUspText("USP Text");
        foodDiningRule.add(restaurantRule);

        boolean foodDiningHighlight = true;
        StaticDetailCriteria staticDetailRequest = new StaticDetailCriteria();
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean foodAndDiningV2 = false;
        boolean foodDiningRevamp = true;
        UGCRatingData foodRatingData = null;
        boolean isAltAcco = false;
        boolean isDhCall = true;

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDining", foodDiningRule, foodDiningHighlight, staticDetailRequest,
                deviceDetails, foodAndDiningV2, foodDiningRevamp, foodRatingData, isAltAcco, isDhCall, true, false);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRestaurants());
        assertEquals(1, result.getRestaurants().size());
        Restaurant restaurant = result.getRestaurants().get(0);
        assertEquals("Restaurant Name", restaurant.getTitle());
        assertEquals("image1.jpg", restaurant.getImageUrl());
        assertEquals("restaurant1", restaurant.getCategoryId());
    }

    @Test
    public void buildFoodDining_WhenFoodDiningRevampIsTrue_ShouldSetCorrectTitle() {
        // Arrange
        List<CommonRules> foodDiningRule = new ArrayList<>();
        boolean foodDiningHighlight = true;
        StaticDetailCriteria staticDetailRequest = new StaticDetailCriteria();
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean foodAndDiningV2 = false;
        boolean foodDiningRevamp = true;
        UGCRatingData foodRatingData = null;
        boolean isAltAcco = true;
        boolean isDhCall = false;

        when(polyglotService.getTranslatedData("FOOD_DINING_CARD_TITLE")).thenReturn("Food and Dining");

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDining", foodDiningRule, foodDiningHighlight, staticDetailRequest,
                deviceDetails, foodAndDiningV2, foodDiningRevamp, foodRatingData, isAltAcco, isDhCall, true, false);

        // Assert
        assertNotNull(result);
        assertEquals("Food and Dining", result.getTitle());
    }

    @Test
    public void buildFoodDining_WhenFoodRatingDataIsPresent_ShouldSetRatingData() {
        // Arrange
        List<CommonRules> foodDiningRule = new ArrayList<>();
        boolean foodDiningHighlight = true;
        StaticDetailCriteria staticDetailRequest = new StaticDetailCriteria();
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean foodAndDiningV2 = false;
        boolean foodDiningRevamp = true;
        UGCRatingData foodRatingData = new UGCRatingData();
        RatingDetail ratingDetail = new RatingDetail();
        ratingDetail.setText("4.5");
        foodRatingData.setSummary(ratingDetail);
        boolean isAltAcco = false;
        boolean isDhCall = false;

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDining", foodDiningRule, foodDiningHighlight, staticDetailRequest,
                deviceDetails, foodAndDiningV2, foodDiningRevamp, foodRatingData, isAltAcco, isDhCall, true, false);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRatingData());
        assertEquals("4.5", result.getRatingData().getSummary().getText());
    }

    @Test
    public void buildFoodDining_WhenCommonRuleHasRules_ShouldAddToSummaryList() {
        // Arrange
        List<CommonRules> foodDiningRule = new ArrayList<>();
        CommonRules commonRule = new CommonRules();
        commonRule.setCategory(FoodAndDiningEnums.IndianFoodOptions.getName());
        List<Rule> rules = new ArrayList<>();
        Rule rule = new Rule();
        rule.setText("Rule Text");
        rule.setIconUrl("ic_nonveg_not_allowed");
        rules.add(rule);
        commonRule.setRules(rules);
        foodDiningRule.add(commonRule);

        boolean foodDiningHighlight = true;
        StaticDetailCriteria staticDetailRequest = new StaticDetailCriteria();
        DeviceDetails deviceDetails = new DeviceDetails();
        boolean foodAndDiningV2 = false;
        boolean foodDiningRevamp = true;
        UGCRatingData foodRatingData = null;
        boolean isAltAcco = false;
        boolean isDhCall = false;

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer,
                "buildFoodDining", foodDiningRule, foodDiningHighlight, staticDetailRequest,
                deviceDetails, foodAndDiningV2, foodDiningRevamp, foodRatingData, isAltAcco, isDhCall, true, false);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getSummary());
        assertEquals(1, result.getSummary().size());
        assertEquals("Rule Text", result.getSummary().get(0));
    }

    @Test
    public void limitAmenitiesCount_WithStringList_ShouldLimitToSix() {
        // Arrange
        List<String> amenities = new ArrayList<>(Arrays.asList(
                "WiFi", "Parking", "Pool", "Gym", "Restaurant", "Bar", "Spa", "Airport Shuttle", "Laundry"
        ));
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(6, amenities.size());
        assertEquals("WiFi", amenities.get(0));
        assertEquals("Parking", amenities.get(1));
        assertEquals("Pool", amenities.get(2));
        assertEquals("Gym", amenities.get(3));
        assertEquals("Restaurant", amenities.get(4));
        assertEquals("Bar", amenities.get(5));
    }

    @Test
    public void limitAmenitiesCount_WithHighlightedAmenityList_ShouldLimitToSix() {
        // Arrange
        List<HighlightedAmenity> amenities = new ArrayList<>();
        for (String name : Arrays.asList("WiFi", "Parking", "Pool", "Gym", "Restaurant", "Bar", "Spa", "Airport Shuttle", "Laundry")) {
            HighlightedAmenity amenity = new HighlightedAmenity();
            amenity.setTitle(name);
            amenities.add(amenity);
        }
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(6, amenities.size());
        assertEquals("WiFi", amenities.get(0).getTitle());
        assertEquals("Parking", amenities.get(1).getTitle());
        assertEquals("Pool", amenities.get(2).getTitle());
        assertEquals("Gym", amenities.get(3).getTitle());
        assertEquals("Restaurant", amenities.get(4).getTitle());
        assertEquals("Bar", amenities.get(5).getTitle());
    }

    @Test
    public void limitAmenitiesCount_WithLessThanSixAmenities_ShouldNotChange() {
        // Arrange
        List<String> amenities = new ArrayList<>(Arrays.asList(
                "WiFi", "Parking", "Pool", "Gym"
        ));
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(4, amenities.size());
        assertEquals("WiFi", amenities.get(0));
        assertEquals("Parking", amenities.get(1));
        assertEquals("Pool", amenities.get(2));
        assertEquals("Gym", amenities.get(3));
    }

    @Test
    public void limitAmenitiesCount_WithExactlySixAmenities_ShouldNotChange() {
        // Arrange
        List<String> amenities = new ArrayList<>(Arrays.asList(
                "WiFi", "Parking", "Pool", "Gym", "Restaurant", "Bar"
        ));
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(6, amenities.size());
        assertEquals("WiFi", amenities.get(0));
        assertEquals("Parking", amenities.get(1));
        assertEquals("Pool", amenities.get(2));
        assertEquals("Gym", amenities.get(3));
        assertEquals("Restaurant", amenities.get(4));
        assertEquals("Bar", amenities.get(5));
    }

    @Test
    public void limitAmenitiesCount_WithNullList_ShouldHandleGracefully() {
        // Arrange
        List<String> amenities = null;
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act & Assert - Should not throw exception
        try {
            ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);
            // If we reach here, the test passes (no exception was thrown)
            assertTrue(true);
        } catch (Exception e) {
            fail("limitAmenitiesCount should handle null list gracefully");
        }
    }

    @Test
    public void limitAmenitiesCount_WithEmptyList_ShouldNotChange() {
        // Arrange
        List<String> amenities = new ArrayList<>();
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(0, amenities.size());
    }

    @Test
    public void limitAmenitiesCount_WithCustomObjects_ShouldLimitToSix() {
        // Arrange - Create a list of custom objects
        class CustomAmenity {
            private String name;
            public CustomAmenity(String name) { this.name = name; }
            public String getName() { return name; }
        }

        List<CustomAmenity> amenities = new ArrayList<>();
        for (String name : Arrays.asList("WiFi", "Parking", "Pool", "Gym", "Restaurant", "Bar", "Spa", "Airport Shuttle", "Laundry")) {
            amenities.add(new CustomAmenity(name));
        }
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(6, amenities.size());
        assertEquals("WiFi", amenities.get(0).getName());
        assertEquals("Parking", amenities.get(1).getName());
        assertEquals("Pool", amenities.get(2).getName());
        assertEquals("Gym", amenities.get(3).getName());
        assertEquals("Restaurant", amenities.get(4).getName());
        assertEquals("Bar", amenities.get(5).getName());
    }

    @Test
    public void limitAmenitiesCount_WithNineAmenities_ShouldRemoveThree() {
        // Arrange
        List<String> amenities = new ArrayList<>(Arrays.asList(
                "WiFi", "Parking", "Pool", "Gym", "Restaurant", "Bar", "Spa", "Airport Shuttle", "Laundry"
        ));
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(6, amenities.size());
        // Check that the first 6 amenities are preserved
        assertEquals("WiFi", amenities.get(0));
        assertEquals("Parking", amenities.get(1));
        assertEquals("Pool", amenities.get(2));
        assertEquals("Gym", amenities.get(3));
        assertEquals("Restaurant", amenities.get(4));
        assertEquals("Bar", amenities.get(5));
        // Check that the last 3 amenities are removed
        assertFalse(amenities.contains("Spa"));
        assertFalse(amenities.contains("Airport Shuttle"));
        assertFalse(amenities.contains("Laundry"));
    }

    @Test
    public void limitAmenitiesCount_WithSevenAmenities_ShouldRemoveOne() {
        // Arrange
        List<String> amenities = new ArrayList<>(Arrays.asList(
                "WiFi", "Parking", "Pool", "Gym", "Restaurant", "Bar", "Spa"
        ));
        boolean serviceApartment = false;
        boolean isAltAcco = false;

        // Act
        ReflectionTestUtils.invokeMethod(staticDetailResponseTransformer, "limitAmenitiesCount", amenities, serviceApartment, isAltAcco);

        // Assert
        assertEquals(6, amenities.size());
        // Check that the first 6 amenities are preserved
        assertEquals("WiFi", amenities.get(0));
        assertEquals("Parking", amenities.get(1));
        assertEquals("Pool", amenities.get(2));
        assertEquals("Gym", amenities.get(3));
        assertEquals("Restaurant", amenities.get(4));
        assertEquals("Bar", amenities.get(5));
        // Check that the last amenity is removed
        assertFalse(amenities.contains("Spa"));
    }

    @Test
    public void buildCategoryCommonRules_WhenCategoryInfoIsNull_ShouldReturnNull() {
        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.CommonRules result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildCategoryCommonRules",
                (Object) null);

        // Assert
        assertNull(result);
    }

    @Test
    public void buildCategoryCommonRules_WhenLanguagesSpokenCategory_ShouldSetCorrectFlags() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.CategoryInfo categoryInfo = new com.mmt.hotels.model.response.staticdata.CategoryInfo();
        categoryInfo.setId("LANGUAGES_SPOKEN");
        categoryInfo.setCategoryName("Languages Spoken");
        categoryInfo.setCategoryDesc("Languages spoken by staff");
        categoryInfo.setRuleDesc(Arrays.asList("English", "Hindi"));
        categoryInfo.setCategoryHeading("Staff Languages");

        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.CommonRules result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildCategoryCommonRules",
                categoryInfo);

        // Assert
        assertNotNull(result);
        assertEquals("LANGUAGESSPOKEN", result.getId());
        assertEquals("Languages Spoken", result.getCategory());
        assertEquals("Languages spoken by staff", result.getCategoryDesc());
        assertEquals("Staff Languages", result.getCategoryHeading());
        assertTrue(result.isShowInDetailHome());
        assertFalse(result.isShowArrowInDetailHome());
        assertFalse(result.getShowInL2Page());
        assertEquals(2, result.getRules().size());
        assertEquals("English", result.getRules().get(0).getText());
        assertEquals("Hindi", result.getRules().get(1).getText());
    }

    @Test
    public void buildCategoryCommonRules_WhenNonLanguageCategory_ShouldSetCorrectFlags() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.CategoryInfo categoryInfo = new com.mmt.hotels.model.response.staticdata.CategoryInfo();
        categoryInfo.setId("BREAKFAST_CHARGES");
        categoryInfo.setCategoryName("Breakfast Charges");
        categoryInfo.setCategoryDesc("Breakfast charges info");
        categoryInfo.setRuleDesc(Collections.singletonList("Complimentary breakfast"));
        categoryInfo.setCategoryHeading("Breakfast Info");

        // Create RuleTableInfo with proper structure
        com.mmt.hotels.model.response.staticdata.RuleTableInfo ruleTableInfo = new com.mmt.hotels.model.response.staticdata.RuleTableInfo();
        ruleTableInfo.setKeyTitle("AGE");
        ruleTableInfo.setValueTitle("COST");
        RuleInfo ruleInfo = new RuleInfo();
        ruleInfo.setKey("up to 1 years");
        ruleInfo.setValue(Collections.singletonList("Free Crib"));
        ruleTableInfo.setInfoList(Collections.singletonList(ruleInfo));
        categoryInfo.setRuleTableInfo(ruleTableInfo);

        // Create the expected return value
        com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo expectedRuleTableInfo =
            new com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo();

        com.mmt.hotels.clientgateway.response.staticdetail.CommonRules result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildCategoryCommonRules",
                categoryInfo);

        // Assert
        assertNotNull(result);
        assertEquals("BREAKFASTCHARGES", result.getId());
        assertEquals("Breakfast Charges", result.getCategory());
        assertEquals("Breakfast charges info", result.getCategoryDesc());
        assertEquals("Breakfast Info", result.getCategoryHeading());
        assertTrue(result.isShowInDetailHome());
        assertTrue(result.isShowArrowInDetailHome());
        assertTrue(result.getShowInL2Page());
        assertNotNull(result.getRuleTableInfo());
        assertEquals(1, result.getRules().size());
        assertEquals("Complimentary breakfast", result.getRules().get(0).getText());
    }

    @Test
    public void buildCategoryCommonRules_WhenNoRuleTableInfo_AndNotLanguageCategory_ShouldReturnNull() {
        // Arrange
        com.mmt.hotels.model.response.staticdata.CategoryInfo categoryInfo = new com.mmt.hotels.model.response.staticdata.CategoryInfo();
        categoryInfo.setId("BREAKFAST_CHARGES");
        categoryInfo.setCategoryName("Breakfast Charges");
        categoryInfo.setRuleDesc(Arrays.asList("Complimentary breakfast"));


        // Act
        com.mmt.hotels.clientgateway.response.staticdetail.CommonRules result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildCategoryCommonRules",
                categoryInfo);

        // Assert
        assertNull(result);
    }

    @Test
    public void buildHouseRulesV2_WhenLanguagesSpokenAndIhHouseRuleUiV2Disabled_ShouldIncludeLanguages() {
        // Arrange
        com.mmt.hotels.clientgateway.response.staticdetail.HouseRules houseRules = new com.mmt.hotels.clientgateway.response.staticdetail.HouseRules();
        List<com.mmt.hotels.model.response.staticdata.CommonRules> foodAndDiningRule = new ArrayList<>();
        List<String> spokenLanguages = Arrays.asList("English", "Hindi", "French");
        com.mmt.hotels.model.response.staticdata.HouseRules houseRulesCB = new com.mmt.hotels.model.response.staticdata.HouseRules();
        com.mmt.hotels.model.response.staticdata.DepositPolicy depositPolicy = null;
        boolean isInternationalHotel = false;
        Map<String, String> expDataMap = new HashMap<>();

        when(polyglotService.getTranslatedData("SPOKEN_LANGUAGE_HEADER")).thenReturn("Languages Spoken");

        // Act
        HouseRulesV2 result = ReflectionTestUtils.invokeMethod(
                staticDetailResponseTransformer,
                "buildHouseRulesV2",
                houseRules, foodAndDiningRule, spokenLanguages, houseRulesCB,
                depositPolicy, isInternationalHotel, expDataMap);

        // Assert
        assertNotNull(result);
        assertEquals("English, Hindi and French", result.getLanguage());
        assertEquals("Languages Spoken", result.getLanguageHeader());
    }

}