package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.gommt.hotels.orchestrator.model.request.common.GeoLocationDetails;
import com.gommt.hotels.orchestrator.model.response.listing.MediaDetails;
import com.gommt.hotels.orchestrator.model.response.listing.VideoDetails;
import com.gommt.hotels.orchestrator.model.response.ugc.ListingReviewDetails;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ibm.icu.text.NumberFormat;
import com.mmt.hotels.clientgateway.businessobjects.AdditionalChargesBO;
import com.mmt.hotels.clientgateway.businessobjects.RequestInputBO;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.MyPartnerConfigConsul;
import com.mmt.hotels.clientgateway.enums.*;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MyPartnerConfig;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.FilterRange;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.request.payment.AddOnNode;
import com.mmt.hotels.clientgateway.request.payment.InsuranceAddOnData;
import com.mmt.hotels.clientgateway.request.payment.TmInsuranceAddOn;
import com.mmt.hotels.clientgateway.request.payment.WidgetData;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.CancellationTimeline;
import com.mmt.hotels.clientgateway.response.FCBenefit;
import com.mmt.hotels.clientgateway.response.Policy;
import com.mmt.hotels.clientgateway.response.PricingDetails;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.availrooms.AffiliateFeeDetail;
import com.mmt.hotels.clientgateway.response.availrooms.Alert;
import com.mmt.hotels.clientgateway.response.availrooms.DoubleBlackInfo;
import com.mmt.hotels.clientgateway.response.availrooms.HotelTag;
import com.mmt.hotels.clientgateway.response.availrooms.MyBizQuickPayConfigBO;
import com.mmt.hotels.clientgateway.response.availrooms.NavigationRule;
import com.mmt.hotels.clientgateway.response.availrooms.PropertyRules;
import com.mmt.hotels.clientgateway.response.availrooms.Rules;
import com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions;
import com.mmt.hotels.clientgateway.response.corporate.ApprovingManager;
import com.mmt.hotels.clientgateway.response.corporate.CorpApprovalInfo;
import com.mmt.hotels.clientgateway.response.corporate.CorpAutobookRequestorConfigBO;
import com.mmt.hotels.clientgateway.response.corporate.CorpRateTags;
import com.mmt.hotels.clientgateway.response.corporate.Reason;
import com.mmt.hotels.clientgateway.response.corporate.ReasonForBooking;
import com.mmt.hotels.clientgateway.response.corporate.ReasonForSkipApproval;
import com.mmt.hotels.clientgateway.response.corporate.ReasonOption;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePriceDetail;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomsResponse;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseSlotPlan;
import com.mmt.hotels.clientgateway.response.emi.EmiPlanType;
import com.mmt.hotels.clientgateway.response.filter.FilterObject;
import com.mmt.hotels.clientgateway.response.listingmap.MetaInfo;
import com.mmt.hotels.clientgateway.response.listingmap.Poi;
import com.mmt.hotels.clientgateway.response.moblanding.*;
import com.mmt.hotels.clientgateway.response.moblanding.LatLong;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.response.rooms.RatePlan;
import com.mmt.hotels.clientgateway.response.rooms.TagInfo;
import com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient;
import com.mmt.hotels.clientgateway.response.searchHotels.GeoLocation;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.staticdetail.*;
import com.mmt.hotels.clientgateway.response.staticdetail.Meal;
import com.mmt.hotels.clientgateway.response.staticdetail.MealDetail;
import com.mmt.hotels.clientgateway.response.thankyou.SelectedSpecialRequests;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.*;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.BlackBenefits;
import com.mmt.hotels.model.persuasion.response.BgGradient;
import com.mmt.hotels.model.persuasion.response.Persuasion;
import com.mmt.hotels.model.request.AddOnState;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.response.HotelBenefitInfo;
import com.mmt.hotels.model.response.addon.InsuranceData;
import com.mmt.hotels.model.response.addon.InsuranceDetails;
import com.mmt.hotels.model.response.addon.TmInsuranceAddOns;
import com.mmt.hotels.model.response.adtech.AdTechPlacementContext;
import com.mmt.hotels.model.response.altaccodata.AltAccoResponse;
import com.mmt.hotels.model.response.altaccodata.CardPayloadResponse;
import com.mmt.hotels.model.response.corporate.AutobookRequestorConfigBO;
import com.mmt.hotels.model.response.corporate.CorpTags;
import com.mmt.hotels.model.response.corporate.GuestHouseReasonOption;
import com.mmt.hotels.model.response.corporate.SkipApprovalReasonOption;
import com.mmt.hotels.model.response.corporate.TravelReasonOption;
import com.mmt.hotels.model.response.emi.NoCostEmiDetails;
import com.mmt.hotels.model.response.flyfish.ConceptSummaryDTO;
import com.mmt.hotels.model.response.flyfish.SubConceptDTO;
import com.mmt.hotels.model.response.flyfish.TravellerRatingSummaryDTO;
import com.mmt.hotels.model.response.listpersonalization.GenericCardPayloadData;
import com.mmt.hotels.model.response.listpersonalization.PriceBucket;
import com.mmt.hotels.model.response.listpersonalization.SpokeCity;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysData;
import com.mmt.hotels.model.response.mypartner.MarkUpDetails;
import com.mmt.hotels.model.response.persuasion.HotelTagType;
import com.mmt.hotels.model.response.persuasion.SelectiveHotelPersuasions;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.pricing.AlternatePriceCard;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.CancellationPolicyTimeline;
import com.mmt.hotels.model.response.pricing.FullPayment;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.LinkedRate;
import com.mmt.hotels.model.response.pricing.jsonviews.DealInfo;
import com.mmt.hotels.model.response.pricing.jsonviews.LongStayBenefits;
import com.mmt.hotels.model.response.prime.DoubleBlackValidateResponse;
import com.mmt.hotels.model.response.searchwrapper.CollectionsResponseBo;
import com.mmt.hotels.model.response.searchwrapper.FeaturedCollections;
import com.mmt.hotels.model.response.searchwrapper.RecommendedSearchContext;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntityAbridged;
import com.mmt.hotels.model.response.staticdata.*;
import com.mmt.hotels.model.response.staticdata.HotelResult;
import com.mmt.hotels.model.response.staticdata.RuleTableInfo;
import com.mmt.hotels.model.response.staticdata.meals.MealClarity;
import com.mmt.hotels.model.response.staticdata.poiinfo.POIInfo;
import com.mmt.hotels.pojo.matchmaker.MatchMakerVideoInfo;
import com.mmt.hotels.util.PromotionalOfferType;
import com.mmt.hotels.util.Tuple;
import com.mmt.model.AttributesFacility;
import com.mmt.model.CardActionData;
import com.mmt.model.Facility;
import com.mmt.model.FacilityGroup;
import com.mmt.model.PropertyPersuasions;
import com.mmt.model.RoomInfo;
import com.mmt.model.SubAttributeFacility;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.MyBiz_Assured;
import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static java.util.Comparator.comparingInt;

@Component
public class CommonResponseTransformer {

	private List<String> safetyCategories;

	private List<String> hotelCategoryTypesPriority;

	private List<String> altAccoCategoryTypesPriority;

	private PersuasionStyle onlyTodayDealTimerStyle;

	private Map<String, Integer> mediaLimitMap;

	@Value("${listing.hotel.media.limit}")
	String listingHotelMediaLimit;

	@Value("${listing.media.limit}")
	int listingMediaLimit;

	@Value("${listing.media.limit.exp}")
	int listingMediaLimitExp;

	@Value("${hotel.category.highest.priority}")
	private String highestPriorityCategory;

	@Value("${hotel.category.selection.algo.version}")
	private int categorySelectionAlgoVersion;

	@Value("${property.rules.max.count}")
	private int propertyRulesMaxCount;

	@Value("${hotel.categories.max.count.allowed}")
	private int maxCategoriesAllowed;

	@Value("#{'${intl.nr.supplier.exclusion}'.split(',')}")
	private List<String> intlNrSupplierExclusionList;

	@Value("${bank.coupon.generic.icon}")
	private String genericBankIcon;

	@Value("${pixel.base.url}")
	private String pixelBaseUrl;

	private Map<String, String> categoryTextToCategoryTypeMap;
	private Map<String, String> mandatoryChargesDisclaimerMap;
	private Map<String, String> mandatoryChargesTransfersShortDescMap;

	private Map<String, String> categoryTypeToCategoryTextMap;

	@Autowired
	private PoliciesResponseTransformer policyResponseTransformer;

	private static Gson gson = new Gson();

	@Value("#{'${corp.segments}'.split(',')}")
	private Set<String> corpSegments;

	@Value("${consul.enable}")
	private boolean consulFlag;

	@Value("${pc.top.section.persuasion.desktop}")
	private String pcTopSectionPersuasionDesktopConfig;

	@Value("${high.demand.background.color}")
	private String highDemandBackgroundColor;

	@Value("${high.demand.title.color}")
	private String highDemandTitleColor;

	@Value("${pc.top.section.persuasion.apps}")
	private String pcTopSectionPersuasionAppsConfig;

	@Value("${personalizedPicks.icon.url.tag}")
	private String personalizedPicksIconUrlTag;

	@Value("${personalizedPicks.style.class.tag.desktop}")
	private String personalizedPicksStyleClassTagDesktop;

	@Value("${personalizedPicks.color.tag.apps}")
	private String personalizedPicksColorTagApps;

	@Value("${placeholder.to.show.section.map}")
	private String placeHolderToShowSectionMap;

	@Value("#{'${pc.top.section.Allowed.Sections}'.split(',')}")
	private Set<String> pcTopSectionPersuasionAllowedSections;

	@Value("${food.rating.thresold}")
	private int foodRatingThresold;

	@Value("${onlytodaydeal.persuasion.type}")
	private String onlyTodayDealPersuasionType;
	@Value("${black.persuasion.type}")
	private String blackPersuasionType;
	@Value("${los.persuasion.type}")
	private String losPersuasionType;
	@Value("${discount.persuasion.type}")
	private String discountPersuasionType;
	@Value("${super.package.persuasion.type}")
	private String superPackagePersuasionType;
	@Value("${detail.page.persuasion.order}")
	private String detailPagePersuasionOrder;

	@Value("#{'${cab.card.city.list}'.split(',')}")
	private Set<String> cabCardCityList;

	@Value("${forex.deeplink.url.apps}")
	private String forexDeeplinkUrlApps;

	@Value("${forex.deeplink.url.dtPwa}")
	private String forexDeeplinkUrlDtPwa;

	@Value("${cabs.forex.deeplink.url.apps}")
	private String cabsforexDeeplinkUrlApps;

	@Value("${cabs.forex.deeplink.url.dtPwa}")
	private String cabsforexDeeplinkUrlDtPwa;

	@Value("${cab.cashback.icon.url}")
	private String cabCashbackIconUrl;

	@Value("${cab.web.view.url}")
	private String cabWebViewUrl;

	@Value("${forex.cashback.icon.url}")
	private String forexCashbackIconUrl;

	@Value("${forex.web.view.url}")
	private String forexWebViewUrl;

	private Set<String> compulsoryChargesTaxTypes;

	@Autowired
	MyPartnerConfigConsul myPartnerConfigConsul;

	@Autowired
	CommonConfigConsul commonConfigConsul;

	@Autowired
	private PropertyManager propManager;

	@Autowired
	private Utility utility;

	@Autowired
	private ObjectMapperUtil objectMapperUtil;


	@Autowired
	private PolyglotService polyglotService;

	@Autowired
	SearchHotelsFactory searchHotelsFactory;

	@Autowired
	PolyglotHelper polyglotHelper;

	@Autowired
	PersuasionUtil persuasionUtil;
	@Autowired
	PricingEngineHelper pricingEngineHelper;

	@Value("${value.stay.icon}")
	private String iconUrl;
	@Value("${value.stay.icon.gcc}")
	private String iconUrlGcc;
	@Value("${value.stay.background}")
	private String valueStayBackground;

	@Value("${listing.driving.duration.buckets}")
	protected String listingDrivingDurationBucket;

	@Value("${bnpl.active.booking.threshold}")
	private int bnplActiveBookingThreshold;

	@Value("${addon.info.hotel.tag}")
	private String addOnInfoTagConfig;

	@Value("${addon.info.most.popular.tag}")
	private String addOnInfoMostPopularTagConfig;

	@Value("${scarcity.review.background}")
	private String scarcityBackground;

	@Value("${bg.gradient.black.popup}")
	private String bgGradientBlackPopup;

	private Map<String, BgGradient> bgGradientBlackPopupMap;

	@Value("${detail.page.fallback.icon.black}")
	private String detailPageFallbackIconBlack;

	@Value("${black.revamp.fallback.bullet.icon}")
	private String blackRevampFallbackBulletIcon;

	@Value("${black.v1.tier.mapping}")
	private String blackV1TierMapping;
	private Map<String, String> blackV1TierMap;

	private Map<String,String> addOnInfoTag;

	private Map<String, String> addOnInfoMostPopularTag;

	protected Map<String, String> listingDrivingDurationBucketMap;


	private Map<String, Map<String, PersuasionResponse>> persuasionResponseMap = null;
	private String mySafetyDataPolyglot;
	private Map<String, HotelCategoryDataWeb> hotelCategoryDataWebMap = null;
	private Map<String, HotelCategoryDataWeb> hotelCategoryDataWebMapNew = null;
	private Map<String, Map<String, HotelCategoryData>> hotelCategoryDataMap = null;
	private Map<String, Map<String, HotelCategoryData>> hotelCategoryDataMapV2 = null;
	private Map<String, Map<String, HotelCategoryData>> hotelCategoryDataMapModified = null;
	private Map<String, HotelCategoryDataWeb> hotelCategoryDataWebMapModified = null;

	private static final String TYPE_FCZPN = "FCZPN";
	private static final String BNPL_DISABLED = "BNPL_DISABLED";

	private static final Logger logger = LoggerFactory.getLogger(CommonResponseTransformer.class);

	private LinkedHashMap<String, Double> discountParameters;

	private int scarcityReviewThreshold=5;

	private List<CardData> businessIdentificationCards;

	private List<CardData> businessIdentificationNonRegisteredUserCard;
	Map<String, String> gccImages;

	private NumberFormat numberFormatter;
	private int deltaPercentageForAltDates=5;

	@Value("${business.rating.icon.url}")
	private String businessRatingIconUrl;

	@PostConstruct
	public void init() {

		if (consulFlag) {
			hotelCategoryTypesPriority = commonConfigConsul.getHotelCategoryPriority();
			altAccoCategoryTypesPriority = commonConfigConsul.getAltAccoCategoryPriority();
			categoryTextToCategoryTypeMap = commonConfigConsul.getCategoryKeyToTextMap();
			businessIdentificationCards = commonConfigConsul.getBusinessIdentificationCards();
			businessIdentificationNonRegisteredUserCard = commonConfigConsul.getBusinessIdentificationNonRegisteredUserCard();
			categoryTextToCategoryTypeMap = Utility.updateKeys(categoryTextToCategoryTypeMap);
			onlyTodayDealTimerStyle = commonConfigConsul.getOneDayDealTimerStyleConfig();

			safetyCategories = commonConfigConsul.getSafetyCategories();
			categoryTypeToCategoryTextMap = MapUtils.isNotEmpty(categoryTextToCategoryTypeMap)?MapUtils.invertMap(categoryTextToCategoryTypeMap):null;
			mediaLimitMap = gson.fromJson(listingHotelMediaLimit, new TypeToken<Map<String, Integer>>() {
			}.getType());
			mySafetyDataPolyglot = commonConfigConsul.getMySafetyData();

			hotelCategoryDataWebMapNew = commonConfigConsul.getHotelCategoryDataWebMapNew();
			hotelCategoryDataWebMapNew = Utility.updateKeys(hotelCategoryDataWebMapNew);
			hotelCategoryDataMap = commonConfigConsul.getHotelCategoryDataMap();
			hotelCategoryDataMap = Utility.updateKeys(hotelCategoryDataMap);
			hotelCategoryDataMapV2 = commonConfigConsul.getHotelCategoryDataMapV2();
			hotelCategoryDataMapV2 = Utility.updateKeys(hotelCategoryDataMapV2);
			scarcityReviewThreshold = commonConfigConsul.getScarcityThreshold();
			mandatoryChargesDisclaimerMap = commonConfigConsul.getMandatoryChargesDisclaimerMap();
			mandatoryChargesTransfersShortDescMap = commonConfigConsul.getMandatoryChargesTransfersShortDescMap();
			compulsoryChargesTaxTypes = commonConfigConsul.getCompulsoryChargesTaxTypes();
			deltaPercentageForAltDates = commonConfigConsul.getDeltaPercentageForAltDates();

			logger.debug("Fetching values from commonConfig consul");
		} else {
			CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);

			hotelCategoryTypesPriority = commonConfig.hotelCategoryPriority();
			altAccoCategoryTypesPriority = commonConfig.altAccoCategoryPriority();
			categoryTextToCategoryTypeMap = commonConfig.categoryKeyToTextMap();
			safetyCategories = commonConfig.safetyCategories();
			categoryTypeToCategoryTextMap = MapUtils.invertMap(categoryTextToCategoryTypeMap);
			mediaLimitMap = gson.fromJson(listingHotelMediaLimit, new TypeToken<Map<String, Integer>>() {
			}.getType());
			mySafetyDataPolyglot = commonConfig.mySafetyData();

			hotelCategoryDataWebMapNew = commonConfig.hotelCategoryDataWebMapNew();
			hotelCategoryDataMap = commonConfig.hotelCategoryDataMap();
			hotelCategoryDataMapV2 = commonConfig.hotelCategoryDataMapV2();
			onlyTodayDealTimerStyle = commonConfig.onlyTodayDealTimerStyle();
		}

		if(consulFlag){
			discountParameters = (LinkedHashMap<String, Double>) myPartnerConfigConsul.getDiscountParameters();
			logger.debug("Fetching values from myPartnerConfig consul");
		}else{
			MyPartnerConfig myPartnerProperty = propManager.getProperty("myPartnerConfig", MyPartnerConfig.class);
			discountParameters = (LinkedHashMap<String, Double>) myPartnerProperty.discountParameters();
		}
		listingDrivingDurationBucketMap = gson.fromJson(listingDrivingDurationBucket, HashMap.class);
		addOnInfoTag = gson.fromJson(addOnInfoTagConfig, HashMap.class);
		addOnInfoMostPopularTag = gson.fromJson(addOnInfoMostPopularTagConfig,HashMap.class);
		bgGradientBlackPopupMap = gson.fromJson(bgGradientBlackPopup, new TypeToken<Map<String, BgGradient>>() {
		}.getType());

		blackV1TierMap = gson.fromJson(blackV1TierMapping, new TypeToken<Map<String, String>>() {
		}.getType());

		// Format the totalCost with commas and without decimals
		numberFormatter = NumberFormat.getNumberInstance(new Locale("en", "IN"));
		numberFormatter.setMaximumFractionDigits(0); // No decimals
		numberFormatter.setMinimumFractionDigits(0); // Ensure no trailing zeros

		CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
		gccImages = commonConfig.gccImages();
	}

	public List<String> getHotelCategories(Set<String> hotelCategories, boolean isAltAcco, final boolean isMyPartner) {

		if (CollectionUtils.isEmpty(hotelCategories)) {
			return null;
		}

		if (CollectionUtils.isNotEmpty(safetyCategories)) {
			Set<String> result = safetyCategories.stream()
					.distinct()
					.filter(hotelCategories::contains)
					.collect(Collectors.toSet());

			//return if any of the safety data is present and only send one category
			if (CollectionUtils.isNotEmpty(result)) {
				return new ArrayList<>(Arrays.asList(result.iterator().next()));
			}
		}

		List<String> applicableCategoryTexts = hotelCategories.stream().filter(categoryText -> categoryTextToCategoryTypeMap.containsKey(categoryText)).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(applicableCategoryTexts))
			applicableCategoryTexts = new ArrayList<>();

		List<String> applicableCategoryTypes = applicableCategoryTexts.stream()
				.map(categoryText -> categoryTextToCategoryTypeMap.get(categoryText))
				.collect(Collectors.toList());
		if (isAltAcco) {
			applicableCategoryTypes.retainAll(altAccoCategoryTypesPriority);
			applicableCategoryTypes.sort(Comparator.comparing(categoryType -> altAccoCategoryTypesPriority.indexOf(categoryType)));
		} else {
			applicableCategoryTypes.retainAll(hotelCategoryTypesPriority);
			applicableCategoryTypes.sort(Comparator.comparing(categoryType -> hotelCategoryTypesPriority.indexOf(categoryType)));
		}

		LinkedHashSet<String> finalCategories = applicableCategoryTypes.stream()
				.map(categoryType -> categoryTypeToCategoryTextMap.get(categoryType))
				.collect(Collectors.toCollection(LinkedHashSet::new));

		if (finalCategories.size() < 2) {
			finalCategories.addAll(hotelCategories);
		}
		List<String> categories = new ArrayList<>(finalCategories);
		categories = categories.subList(0, Math.min(maxCategoriesAllowed, categories.size()));
		if (isMyPartner)
			categories.add(EXCLUSIVE_DEAL_TAG);
		return categories;
	}

	public PropertyRules getImportantInfoSection(RequestInputBO inputBo) {
		PropertyRules propertyRules = new PropertyRules();
		propertyRules.setPropertyType(inputBo.getPropertyType());
		propertyRules.setDescription(polyglotService.getTranslatedData(ConstantsTranslation.PROPERTY_RULES_DISCLAIMER_TEXT));
		propertyRules.setRules(buildPropertyRules(inputBo));
		propertyRules.setNavigationRules(buildNavigationRules(inputBo));

		if (CollectionUtils.isEmpty(propertyRules.getRules())) {
			propertyRules.setRules(null);
			propertyRules.setNoRulesAvailable(true);
		}

		return propertyRules;
	}

	private List<NavigationRule> buildNavigationRules(RequestInputBO inputBo) {
		List<NavigationRule> navigationRules = new ArrayList<>();

		// 1. Extra bed policy
		if (inputBo.getHouseRules() != null && CollectionUtils.isNotEmpty(inputBo.getHouseRules().getCategoryInfoList())) {
			for (CategoryInfo categoryInfo : inputBo.getHouseRules().getCategoryInfoList()) {
				if (categoryInfo.getId().equalsIgnoreCase("EXTRA_BED_POLICY")) {
					RuleTableInfo ruleTableInfo = Utility.getParsedRuleTableInfo(categoryInfo.getRuleTableInfo());
					if (ruleTableInfo != null) {
						NavigationRule navigationRule = new NavigationRule();
						navigationRule.setId(categoryInfo.getId());
						navigationRule.setCategoryDesc(categoryInfo.getCategoryDesc());
						navigationRule.setCategoryName(categoryInfo.getCategoryName());
						navigationRules.add(navigationRule);
					}
				}
			}
		}

		// 2. Breakfast charges policy
		if (inputBo.getHouseRules() != null && CollectionUtils.isNotEmpty(inputBo.getHouseRules().getCategoryInfoList())) {
			for (CategoryInfo categoryInfo : inputBo.getHouseRules().getCategoryInfoList()) {
				if (categoryInfo.getId().equalsIgnoreCase("BREAKFAST_CHARGES")) {
					RuleTableInfo ruleTableInfo = Utility.getParsedRuleTableInfo(categoryInfo.getRuleTableInfo());
					if (ruleTableInfo != null) {
						NavigationRule navigationRule = new NavigationRule();
						navigationRule.setId(categoryInfo.getId());
						navigationRule.setCategoryDesc(categoryInfo.getCategoryDesc());
						navigationRule.setCategoryName(categoryInfo.getCategoryName());
						navigationRules.add(navigationRule);
					}
				}
			}
		}

		if (inputBo.getDepositPolicy() != null) {
			RuleTableInfo ruleTableInfo = Utility.getParsedRuleTableInfo(inputBo.getDepositPolicy().getRuleTableInfo());
			if (ruleTableInfo != null) {
				NavigationRule navigationRule = new NavigationRule();
				navigationRule.setCategoryName(inputBo.getDepositPolicy().getCategoryName());
				navigationRule.setId(inputBo.getDepositPolicy().getId());
				navigationRule.setCategoryDesc(inputBo.getDepositPolicy().getCategoryDesc());
				navigationRules.add(navigationRule);
			}
		}

		return navigationRules;
	}

	public List<Rules> buildPropertyRules(RequestInputBO inputBo) {
		List<Rules> rulesList = new ArrayList<>();

		// 1. notices
		if (CollectionUtils.isNotEmpty(inputBo.getNotices())) {
			for (Notices notice : inputBo.getNotices()) {
				if (StringUtils.isBlank(notice.getDescription()))
					continue;
				Rules propertyRule = new Rules();
				propertyRule.setIconType(IconType.DEFAULT.name());
				propertyRule.setTitle(notice.getDescription());
				rulesList.add(propertyRule);
				if (rulesList.size() == propertyRulesMaxCount)
					return rulesList;
			}
		}

		// 2. checkIn restriction & confirmation policy when available
		if (inputBo.getCheckinPolicy() != null && StringUtils.isNotBlank(inputBo.getCheckinPolicy().getDescription())) {
			Rules propertyRule = new Rules();
			propertyRule.setIconType(IconType.DEFAULT.name());
			propertyRule.setTitle(inputBo.getCheckinPolicy().getDescription());
			rulesList.add(propertyRule);
			if (rulesList.size() == propertyRulesMaxCount)
				return rulesList;
		}
		if (inputBo.getConfirmationPolicy() != null && StringUtils.isNotBlank(inputBo.getConfirmationPolicy().getDescription())) {
			Rules propertyRule = new Rules();
			propertyRule.setIconType(IconType.DEFAULT.name());
			propertyRule.setTitle(inputBo.getConfirmationPolicy().getDescription());

			rulesList.add(propertyRule);
			if (rulesList.size() == propertyRulesMaxCount)
				return rulesList;
		}

		// 3. Pah_with_cc + FC/NR text
		if (inputBo.isPahWithCC()) {
			Rules propertyRule = new Rules();
			propertyRule.setIconType(IconType.DEFAULT.name());
			if (BookedCancellationPolicyType.NR.name().equalsIgnoreCase(inputBo.getCancellationPolicyType())) {
				propertyRule.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.INTL_PAH_NON_REFUNDABLE_TEXT));
			} else if(SUPPLIER_INGO.equalsIgnoreCase(inputBo.getSupplierCode())){
				propertyRule.setTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.INTL_PAH_FREE_CANCELLATION_TEXT), inputBo.getCancellationDate()));
			}else{
				propertyRule.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.INTL_PAH_FC_NON_INGO_POLICY_TEXT));
			}
			rulesList.add(propertyRule);
			if (rulesList.size() == propertyRulesMaxCount)
				return rulesList;
		}

		// 4. must read policies
		if (CollectionUtils.isNotEmpty(inputBo.getMustReadRules())) {
			for (String mustRead : inputBo.getMustReadRules()) {
				Rules propertyRule = new Rules();
				propertyRule.setIconType(IconType.DEFAULT.name());
				propertyRule.setTitle(mustRead);
				rulesList.add(propertyRule);
				if (rulesList.size() == propertyRulesMaxCount)
					return rulesList;
			}
		}

		// 5. Intl NR text for suppliers other than ingo, expedia
		if (!Constants.DOM_COUNTRY.equalsIgnoreCase(inputBo.getCountryCode()) &&
				BookedCancellationPolicyType.NR.name().equalsIgnoreCase(inputBo.getCancellationPolicyType())) {
			boolean excluded = isSupplierExcluded(inputBo.getSupplierCode());
			if (!excluded) {
				Rules propertyRule = new Rules();
				propertyRule.setIconType(IconType.DEFAULT.name());
				propertyRule.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.INTL_NR_SUPPLIER_SPECIFIC_TEXT));
				rulesList.add(propertyRule);
				if (rulesList.size() == propertyRulesMaxCount)
					return rulesList;
			}
		}

		// 6. house rules
		if (inputBo.getHouseRules() != null) {
			List<Policy> houseRulePolices = policyResponseTransformer.buildHouseRules(inputBo.getHouseRules());
			if (CollectionUtils.isNotEmpty(houseRulePolices)) {
				for (Policy houseRulePolicy : houseRulePolices) {
					if (CollectionUtils.isNotEmpty(houseRulePolicy.getRules())) {
						for (String policy : houseRulePolicy.getRules()) {
							// must read & house rules may have some common rules, so ignore duplicates
							if (!checkIfRuleDuplicate(rulesList, policy)) {
								Rules propertyRule = new Rules();
								propertyRule.setIconType(IconType.DEFAULT.name());
								propertyRule.setTitle(policy);
								rulesList.add(propertyRule);
								if (rulesList.size() == propertyRulesMaxCount)
									return rulesList;
							}
						}
					}
				}
			}
		}
		return rulesList;
	}

	private boolean checkIfRuleDuplicate(List<Rules> rulesList, String currentRule) {
		Optional<Rules> duplicateRule = rulesList.stream().filter(rule -> currentRule.equalsIgnoreCase(rule.getTitle())).findAny();
		return duplicateRule.isPresent();
	}

	private boolean isSupplierExcluded(String supplierCode) {
		if (StringUtils.isNotBlank(supplierCode)) {
			for (String supplier : intlNrSupplierExclusionList) {
				if (supplierCode.startsWith(supplier))
					return true;
			}
		}
		return false;
	}


	public TotalPricing getTotalPricing(DisplayPriceBreakDown displayPriceBrkDwn, String countryCode, String payMode, boolean isCorp, String segmentId, String expData,
										boolean groupBookingFunnel, String priceBreakuptext, boolean cbrAvailable, final MarkUpDetails markUpDetails, boolean isMetaTraffic, HotelBenefitInfo benefitInfo, String currency, boolean isNewPropertyOfferApplicable) {
		TotalPricing totalPricing = new TotalPricing();
		if (displayPriceBrkDwn != null) {
			Map<String, String> expDataMap = utility.getExpDataMap(expData);
			boolean isIhCashbackSectionEnable = utility.isExperimentTrue(expDataMap, ihCashbackSectionExp);
			int ancillaryVariant = MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT.getKey()) ? Integer.parseInt(expDataMap.get(ExperimentKeys.ANCILLARY_DISPLAY_PRICE_VARIANT.getKey())) : 0;
			totalPricing.setPinCodeMandatory(displayPriceBrkDwn.isPinCodeMandatory());
			totalPricing.setDetails(getPricingDetails(displayPriceBrkDwn, countryCode, payMode, isCorp, segmentId, expData, groupBookingFunnel,
					utility.isExperimentTrue(expDataMap, Constants.MY_PARTNER_MOVE_TO_TDS_TAX_STRUCTURE), priceBreakuptext, cbrAvailable, false, displayPriceBrkDwn.getCouponInfo()!=null?displayPriceBrkDwn.getCouponInfo().getExtraDiscountType():Constants.EMPTY_STRING,isMetaTraffic, isNewPropertyOfferApplicable));
			totalPricing.setPartnerDetails(getPartnerDetails(totalPricing.getDetails(),pricingEngineHelper.getMarkUpForHotels(markUpDetails,displayPriceBrkDwn.getDisplayPrice())));

			boolean showReviewOffersCategory = utility.showReviewOffersCategory(expDataMap);
			totalPricing.setCoupons(getCouponDetails(displayPriceBrkDwn, isIhCashbackSectionEnable,ancillaryVariant,showReviewOffersCategory));

			if (CollectionUtils.isEmpty(totalPricing.getCoupons())) {
				totalPricing.setNoCouponText(polyglotService.getTranslatedData(ConstantsTranslation.NO_COUPON_AVAILABLE_TEXT));
			}
			totalPricing.setPricingKey(displayPriceBrkDwn.getPricingKey());

			if (displayPriceBrkDwn.getBlackInfo() != null && (ANDROID.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_IOS.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())))) {
				setPricePersuasionBlackInfo(displayPriceBrkDwn.getBlackInfo(), totalPricing);
			}


			if (benefitInfo!= null && (ANDROID.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_IOS.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) ||
					DEVICE_OS_PWA.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_OS_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())))) {
				setPricePersuasionHotelBenefitInfo(benefitInfo, totalPricing);
			}

		}
		if(B2C.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue())) && !Utility.isGccOrKsa()){
			totalPricing.setCouponSubtext(polyglotService.getTranslatedData(GIFT_CARD_TEXT));
		}
		totalPricing.setCurrency(currency);
		return totalPricing;
	}


	public PriceDetail getPartnerDetails(PriceDetail priceDetail, final double markUp) {
		final PriceDetail priceWithMarkUp = new PriceDetail();
		if (priceDetail != null && Objects.nonNull(priceDetail.getPrice()) && Objects.nonNull(priceDetail.getPriceWithTax()) && Objects.nonNull(priceDetail.getDiscountedPrice()) && Objects.nonNull(priceDetail.getDiscountedPriceWithTax())) {
			priceWithMarkUp.setPrice(priceDetail.getPrice() + markUp);
			priceWithMarkUp.setPriceWithTax(priceDetail.getPriceWithTax() + markUp);
			priceWithMarkUp.setDiscountedPrice(priceDetail.getDiscountedPrice() + markUp);
			priceWithMarkUp.setDiscountedPriceWithTax(priceDetail.getDiscountedPriceWithTax() + markUp);
			return priceWithMarkUp;
		}
		return null;
	}



	public List<PricingDetails> getPartnerDetails(final List<PricingDetails> details, final double markUp) {
		List<PricingDetails> partnerDetails = new ArrayList<>();
		if(Objects.isNull(details))
			return null;
		for (PricingDetails detail : details) {
			if (Constants.BASE_FARE_KEY.equalsIgnoreCase(detail.getKey()) || Constants.TOTAL_AMOUNT_KEY.equalsIgnoreCase(detail.getKey())) {
				PricingDetails pricingDetails = new PricingDetails();
				BeanUtils.copyProperties(detail, pricingDetails);
				pricingDetails.setAmount(markUp + detail.getAmount());
				partnerDetails.add(pricingDetails);
			}
		}
		return partnerDetails;
	}
	public void setPricePersuasionBlackInfo(com.mmt.hotels.model.response.mmtprime.BlackInfo blackInfo, TotalPricing totalPricing) {
		if (totalPricing != null) {
			Persuasion blackPersuasion = persuasionUtil.buildBlackPersuasionForReviewPage(blackInfo, blackInfo != null && blackInfo.isBlackRevamp());
			if (blackPersuasion != null) {
				if (totalPricing.getPricePersuasions() != null) {
					totalPricing.getPricePersuasions().put(PLACEHOLDER_PRICE_BOTTOM, blackPersuasion);
				} else {
					totalPricing.setPricePersuasions(new HashMap<>());
					totalPricing.getPricePersuasions().put(PLACEHOLDER_PRICE_BOTTOM, blackPersuasion);
				}
			}
		}
	}
	//set benefitInfo in the pricing persuasion
	public void setPricePersuasionHotelBenefitInfo(com.mmt.hotels.model.response.HotelBenefitInfo benefitInfo, TotalPricing totalPricing) {
		if (totalPricing != null && benefitInfo != null ) {
			Persuasion benefitPersuasion = persuasionUtil.buildHotelBenefitPersuasionForReviewPage(benefitInfo);
			if (benefitPersuasion != null) {
				if (totalPricing.getPricePersuasions() != null) {
					totalPricing.getPricePersuasions().put(PLACEHOLDER_PRICE_BOTTOM_M1, benefitPersuasion);
				} else {
					totalPricing.setPricePersuasions(new HashMap<>());
					totalPricing.getPricePersuasions().put(PLACEHOLDER_PRICE_BOTTOM_M1, benefitPersuasion);
				}
			}
		}
	}

	public FullPayment buildNoCostEmiDetailAndUpdateFullPayment(List<NoCostEmiDetails> noCostEmiDetailsList,
																TotalPricing totalPricing, com.mmt.hotels.clientgateway.response.FullPayment fullPaymentCG, BNPLDetails bnplDetails) {
		if (CollectionUtils.isEmpty(noCostEmiDetailsList)) {
			return null;
		}

		NoCostEmiDetails noCostEmiDetails = noCostEmiDetailsList.get(0);
		if (noCostEmiDetails == null || noCostEmiDetails.getTenure() == 0 || noCostEmiDetails.getEmiAmount() == 0) {
			return null;
		}

		double insuranceAndCharityAmount = getInsuranceAndCharityAmount(totalPricing);
		if (insuranceAndCharityAmount > 0.0) {
			noCostEmiDetails.setEmiAmount(noCostEmiDetails.getEmiAmount() + insuranceAndCharityAmount / noCostEmiDetails.getTenure());
		}

		EMIPlanDetail emiPlanDetail = new EMIPlanDetail();
		String emiAmount = numberFormatter.format(noCostEmiDetails.getEmiAmount());
		StringBuilder emiPlanDescBuilder = new StringBuilder().append(Currency.INR.getCurrencySymbol()).append(emiAmount).append("/month");
		emiPlanDetail.setEmiPlanDesc(emiPlanDescBuilder.toString());
		emiPlanDetail.setMessage(polyglotService.getTranslatedData(NO_COST_EMI_PAYMENT_TITLE_TEXT));
		emiPlanDetail.setCtaText(polyglotService.getTranslatedData(VIEW_PLANS_CTA_TEXT));
		totalPricing.setEmiPlanDetail(emiPlanDetail);

		double totalAmount = totalPricing.getDetails().stream()
				.filter(f -> TOTAL_AMOUNT_KEY.equalsIgnoreCase(f.getKey()))
				.findFirst()
				.map(PricingDetails::getAmount) // Extract amount if present
				.orElse(0.0); // Default value if not present

		if (bnplDetails != null) {
			String subText = MessageFormat.format(polyglotService.getTranslatedData(NO_COST_EMI_FULL_PAYMENT_SUB_TITLE_TEXT), noCostEmiDetails.getTenure(), emiAmount);
			if (fullPaymentCG == null) {
				FullPayment fullPaymentHES = new FullPayment();
				fullPaymentHES.setFullPaymentText(MessageFormat.format(polyglotService.getTranslatedData(PAY_FULL_AMOUNT_NOW_TITLE_TEXT), Math.round(totalAmount)));
				fullPaymentHES.setFinalPrice((int) totalAmount);
				fullPaymentHES.setFullPaymentSubText(subText);
				return fullPaymentHES;
			} else {
				fullPaymentCG.setFullPaymentSubText(subText);
			}
		}
		return null;
	}

	private double getInsuranceAndCharityAmount(TotalPricing totalPricing) {
		if (totalPricing != null && CollectionUtils.isNotEmpty(totalPricing.getDetails())) {
			return totalPricing.getDetails().stream()
					.filter(Objects::nonNull)
					.map(pricingDetails -> {
						double amount = pricingDetails.getAmount();
						String key = pricingDetails.getKey();
						return (TOTAL_INSURANCE.equalsIgnoreCase(key) || CHARITY.equalsIgnoreCase(key)) ? amount : 0.0;
					})
					.reduce(0.0, Double::sum);

		}
		return 0.0;
	}

	public List<PricingDetails> getPricingDetails(DisplayPriceBreakDown displayPriceBrkDwn, String countryCode, String payMode, boolean isCorp, String segmentId, String expData,
												  boolean groupBookingFunnel, boolean myPartnerMoveToTdsTaxStructure, String priceBreakuptext, boolean cbrAvailable, boolean isValidateCouponFlow, String extraDiscountType, boolean isMetaTraffic, boolean isNewPropertyOfferApplicable) {
		if (displayPriceBrkDwn != null) {
			String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
			List<PricingDetails> pricingDetails = new ArrayList<>();
			buildInsuranceBreakup(pricingDetails, displayPriceBrkDwn, priceBreakuptext);
			//need to show charity breakUp only in total-pricing & validate-coupon flow
			if(isValidateCouponFlow) {
				buildCharityAddonBreakUp(pricingDetails, displayPriceBrkDwn);
			}
			buildBaseFare(pricingDetails, displayPriceBrkDwn);
			buildBaseFareWithTax(pricingDetails, displayPriceBrkDwn, expData, groupBookingFunnel);
			buildSMESubscriptionBreakup(pricingDetails, displayPriceBrkDwn);
			buildTcsAmount(pricingDetails, displayPriceBrkDwn);

			/* HTL-42386: Add new price map nodes if MyPartnerMoveToTdsTaxStructure is enabled.
			   PriceMap will contain the following:
			   For details page: Price, Offer Price, Partner Commission with Commission Via e- Coupon Discount (Without HCP) and Commission Via Promo Cash.
			   For review page: Base Price, Hotel Discount including HCP, Partner Commission including the breakup of Promocash and Coupon Discount without HCP,
			                    Convenience Fees, Hotel Taxes (Without any HCP tax adjustment), TDS.
			 */
			if (myPartnerMoveToTdsTaxStructure) {
				if (utility.isReviewPageAPI(controller)) {
					// Build partner commission, hotel discount and TDS for review page.
					buildPartnerCommission(pricingDetails, displayPriceBrkDwn);
					buildHotelFundedDiscount(pricingDetails, displayPriceBrkDwn);
					buildHotelDiscount(pricingDetails, displayPriceBrkDwn);
					buildTDS(pricingDetails, displayPriceBrkDwn);
				}
			} else {
				buildTotalDiscounts(pricingDetails, displayPriceBrkDwn, isCorp, segmentId, expData,extraDiscountType, isMetaTraffic, isNewPropertyOfferApplicable);
				buildPriceAfterDiscount(pricingDetails, displayPriceBrkDwn);
			}

			buildWallet(pricingDetails, displayPriceBrkDwn);
			buildTaxesAndServiceFee(pricingDetails, displayPriceBrkDwn, countryCode, expData, cbrAvailable);
			if (Utility.isPahOnlyPaymode(payMode))
				buildAmountYouPayingNow(pricingDetails, displayPriceBrkDwn, payMode);
			buildTotalAmount(pricingDetails, displayPriceBrkDwn, payMode, controller);
			if (utility.isDetailPageAPI(controller) && displayPriceBrkDwn.getFlexiCancellationCharges() > 0d) {
				buildFlexiCancelBaseFare(pricingDetails,displayPriceBrkDwn);
				buildFlexiCancelAmount(pricingDetails, displayPriceBrkDwn, payMode);
				buildFlexiCancelTotalAmount(pricingDetails, displayPriceBrkDwn, payMode);
			} else if (utility.isReviewPageAPI(controller) && displayPriceBrkDwn.getFlexiCancellationCharges() > 0d) {
				buildFlexiCancelAmount(pricingDetails, displayPriceBrkDwn, payMode);
			}
			return pricingDetails;
		}
		return null;
	}

	private void buildTcsAmount(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
		if (displayPriceBrkDwn.getTcsAmount() > 0.0d) {
			//Add TCS amount to total amount
			displayPriceBrkDwn.setDisplayPrice(displayPriceBrkDwn.getDisplayPrice() + displayPriceBrkDwn.getTcsAmount());

			PricingDetails tcsAmount = new PricingDetails();
			tcsAmount.setAmount(displayPriceBrkDwn.getTcsAmount());
			tcsAmount.setKey(Constants.TCS_AMOUNT);
			tcsAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TCS_AMOUNT_LABEL));
			tcsAmount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			pricingDetails.add(tcsAmount);
		}
	}

	private void buildFlexiCancelBaseFare(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
		if (displayPriceBrkDwn.getFlexiCancellationCharges() > 0.0d) {
			PricingDetails baseFare = new PricingDetails();
			baseFare.setAmount(displayPriceBrkDwn.getBasePrice() + displayPriceBrkDwn.getFlexiCancellationCharges());
			baseFare.setKey(Constants.FLEXI_CANCEL_BASE_FARE_KEY);
			baseFare.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.BASE_FARE_LABEL));
			baseFare.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			pricingDetails.add(baseFare);
		}
	}

	private void buildFlexiCancelAmount(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn, String payMode) {
		if (displayPriceBrkDwn.getFlexiCancellationCharges() >0.0d) {
			PricingDetails totalAmount = new PricingDetails();
			totalAmount.setAmount(displayPriceBrkDwn.getFlexiCancellationCharges());
			totalAmount.setKey(FLEXI_CANCEL_AMOUNT);
			totalAmount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			if (Utility.isPahOnlyPaymode(payMode)) {
				totalAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.AMOUNT_YOU_PAYING_AT_HOTEL_LABEL));
			} else {
				totalAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.FLEXI_CANCEL_CHARGES_LABEL));
			}
			pricingDetails.add(totalAmount);
		}
	}

	private void buildFlexiCancelTotalAmount(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn, String payMode) {
		PricingDetails totalAmount = new PricingDetails();
		totalAmount.setAmount(displayPriceBrkDwn.getDisplayPriceWithCfar());
		totalAmount.setKey(FLEXI_CANCEL_TOTAL_AMOUNT_KEY);
		totalAmount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
		if (Utility.isPahOnlyPaymode(payMode)) {
			totalAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.AMOUNT_YOU_PAYING_AT_HOTEL_LABEL));
		} else {
			totalAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_AMOUNT_LABEL));
		}
		pricingDetails.add(totalAmount);
	}

	/**
	 * This method creates new Hotel Funded Discount node in review page
	 * @param pricingDetails details of price breakdown in review page
	 * @param displayPriceBrkDwn contains hotelFundedDiscount value
	 */
	private void buildHotelFundedDiscount(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
		if (displayPriceBrkDwn.getEffectiveDiscount() <= 0d) return;
		PricingDetails hotelFundedDiscount = new PricingDetails();
		hotelFundedDiscount.setAmount(displayPriceBrkDwn.getEffectiveDiscount());
		hotelFundedDiscount.setKey(HOTEL_FUNDED_DISCOUNT);
		hotelFundedDiscount.setLabel(polyglotService.getTranslatedData(HOTEL_FUNDED_DISCOUNT_LABEL));
		hotelFundedDiscount.setType(polyglotService.getTranslatedData(PRICE_TYPE_DIFF));
		pricingDetails.add(hotelFundedDiscount);
	}

	/**
	 * Build TDS amount for MyPartner. TDS is calculated at HES end as follows:
	 * tdsAmount = ((cdfDiscount + wallet) * tdsSlabPercentage) / 100
	 *
	 * @param pricingDetails
	 * @param displayPriceBrkDwn
	 */
	private void buildTDS(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
		double tdsAmount = displayPriceBrkDwn.getTdsAmount();
		if (tdsAmount > 0.0d) {
			PricingDetails hotelDiscount = new PricingDetails();
			hotelDiscount.setAmount(tdsAmount);
			hotelDiscount.setKey(Constants.TDS_KEY);
			hotelDiscount.setLabel(polyglotService.getTranslatedData(TDS_LABEL));
			hotelDiscount.setType(polyglotService.getTranslatedData(PRICE_TYPE_SUM));
			pricingDetails.add(hotelDiscount);
		}
	}

	/**
	 * Build hotel discount for MyPartner. Hotel discount is calculated as follows:
	 * totalHotelDiscountAmount = mmtDiscount + blackDiscount + hotelierCouponDiscount + gstDiscountHotelierCoupon
	 *
	 * @param pricingDetails
	 * @param displayPriceBrkDwn
	 */
	private void buildHotelDiscount(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
		double totalHotelDiscountAmount = Utility.round(displayPriceBrkDwn.getMmtDiscount() + displayPriceBrkDwn.getBlackDiscount() + displayPriceBrkDwn.getHotelierCouponDiscount(), 2);
		if (totalHotelDiscountAmount > 0.0d) {
			PricingDetails hotelDiscount = new PricingDetails();
			hotelDiscount.setAmount(totalHotelDiscountAmount);
			hotelDiscount.setKey(Constants.HOTEL_DISCOUNT_KEY);
			hotelDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.HOTEL_DISCOUNT_LABEL));
			hotelDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_DIFF));
			pricingDetails.add(hotelDiscount);
		}
	}

	/**
	 * Build partner commission for MyPartner. Partner commission is calculated as sum of Coupon Discount without HCP and Promo Cash.
	 *
	 * @param pricingDetails
	 * @param displayPriceBrkDwn
	 */
	private void buildPartnerCommission(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
		double totalPartnerCommission = 0.0d;
		List<PricingDetails> priceBreakup = new ArrayList<>();
		if (displayPriceBrkDwn.getWallet() > 0.0d) {
			PricingDetails wallet = new PricingDetails();
			wallet.setAmount(displayPriceBrkDwn.getWallet());
			wallet.setKey(Constants.WALLET_KEY);
			wallet.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.PROMO_CASH_MY_PARTNER));
			wallet.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			priceBreakup.add(wallet);
			totalPartnerCommission += wallet.getAmount();
		}

		if (displayPriceBrkDwn.getCdfDiscount() - displayPriceBrkDwn.getEffectiveDiscount() > 0.0d) {
			PricingDetails cdfDiscount = new PricingDetails();
			cdfDiscount.setAmount(displayPriceBrkDwn.getCdfDiscount() - displayPriceBrkDwn.getEffectiveDiscount());
			cdfDiscount.setKey(Constants.CDF_DISCOUNT_KEY);
			String cdfDiscountLabel = polyglotService.getTranslatedData(CDF_DISCOUNT_LABEL_MY_PARTNER);
			cdfDiscountLabel = StringUtils.replace(cdfDiscountLabel, "{COUPON_CODE}", displayPriceBrkDwn.getCouponInfo() != null ? displayPriceBrkDwn.getCouponInfo().getCouponCode() : StringUtils.EMPTY);
			cdfDiscount.setLabel(cdfDiscountLabel);
			cdfDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			priceBreakup.add(cdfDiscount);
			totalPartnerCommission += cdfDiscount.getAmount();
		}

		if (totalPartnerCommission > 0.0d) {
			PricingDetails totalDiscount = new PricingDetails();
			totalDiscount.setAmount(totalPartnerCommission);
			totalDiscount.setKey(Constants.PARTNER_COMMISSION_KEY);
			totalDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.PARTNER_COMMISSION_LABEL));
			totalDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_DIFF));
			totalDiscount.setBreakup(priceBreakup);
			pricingDetails.add(totalDiscount);
		}
	}

	private void buildAmountYouPayingNow(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn, String payMode) {
		PricingDetails totalAmount = new PricingDetails();
		totalAmount.setAmount(0.0d); // pah case
		totalAmount.setKey(Constants.AMOUNT_YOU_PAYING_NOW_KEY);
		totalAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.AMOUNT_YOU_PAYING_NOW_LABEL));
		if (!Utility.isPahWithCCPaymode(payMode))
			totalAmount.setSubLine(polyglotService.getTranslatedData(ConstantsTranslation.NO_CARD_REQUIRED_SUBLINE));
		totalAmount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
		pricingDetails.add(totalAmount);
	}

	public List<Coupon> getCouponDetails(DisplayPriceBreakDown displayPriceBrkDwn, boolean ihCashbackSectionEnable, int ancillaryVariant, boolean showReviewOffersCategory) {
		if (displayPriceBrkDwn.getCouponInfo() != null) {
			List<Coupon> coupons = new ArrayList<>();
			Coupon coupon = new Coupon();
			coupon.setCode(displayPriceBrkDwn.getCouponInfo().getCouponCode());

			if (showReviewOffersCategory) {
				coupon.setBankOffer(displayPriceBrkDwn.getCouponInfo().isBankOffer());
			} else {
				coupon.setBankOffer(false);
			}

			coupon.setCouponAmount(displayPriceBrkDwn.getCouponInfo().getDiscountAmount() != null ? displayPriceBrkDwn.getCouponInfo().getDiscountAmount() : 0.0);
			if(displayPriceBrkDwn.getCouponInfo().getForexCouponDetails() != null){
				coupon.setCouponType(COUPON_TYPE_BENEFIT);
				coupon.setCouponAmount(displayPriceBrkDwn.getCouponInfo().getForexCashbackAmount());
				if(!ihCashbackSectionEnable) {
					utility.forexCouponsNodeAddition(displayPriceBrkDwn.getCouponInfo(), coupon, Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())), 0, ancillaryVariant);
				}
			}
			coupon.setDescription(displayPriceBrkDwn.getCouponInfo().getDescription());
			coupon.setAutoApplicable(displayPriceBrkDwn.getCouponInfo().isAutoApplicable());
			coupon.setBnplAllowed(displayPriceBrkDwn.getCouponInfo().getBnplAllowed() != null ? displayPriceBrkDwn.getCouponInfo().getBnplAllowed() : false);
			coupon.setTncUrl(displayPriceBrkDwn.getCouponInfo().getTncUrl());
			coupon.setDisabled(displayPriceBrkDwn.getCouponInfo().isDisabled());
			coupon.setPromoIcon(StringUtils.isNotEmpty(displayPriceBrkDwn.getCouponInfo().getPromoIconLink()) ? displayPriceBrkDwn.getCouponInfo().getPromoIconLink() : genericBankIcon);
			coupon.setNoCostEmiApplicable(displayPriceBrkDwn.getCouponInfo().isNoCostEmiApplicable());
			if (StringUtils.isNotEmpty(displayPriceBrkDwn.getCouponInfo().getBankName())) coupon.setBankName(displayPriceBrkDwn.getCouponInfo().getBankName());
			coupons.add(coupon);
			return coupons;
		}
		return null;
	}

	private void buildBaseFare(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
		if (displayPriceBrkDwn.getBasePrice() > 0.0d) {
			PricingDetails baseFare = new PricingDetails();
			baseFare.setAmount(displayPriceBrkDwn.getBasePrice());
			baseFare.setKey(Constants.BASE_FARE_KEY);
			baseFare.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.BASE_FARE_LABEL));
			baseFare.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			pricingDetails.add(baseFare);
		}
	}

	private void buildBaseFareWithTax(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn, String expData, boolean groupBookingFunnel) {
		if (displayPriceBrkDwn.getNonDiscountedPrice() > 0.0d && utility.isExpPdoPrnt(expData) && groupBookingFunnel) {
			PricingDetails baseFareWithTax = new PricingDetails();
			baseFareWithTax.setAmount(displayPriceBrkDwn.getNonDiscountedPrice()+(displayPriceBrkDwn.isTaxIncluded()?0:displayPriceBrkDwn.getTotalTax()));
			baseFareWithTax.setKey(Constants.BASE_FARE_WITH_TAX_KEY);
			baseFareWithTax.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.BASE_FARE_WITH_TAX_LABEL));
			baseFareWithTax.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			pricingDetails.add(baseFareWithTax);
		}
	}

	public void buildInsuranceBreakup(List<PricingDetails> priceDetails, DisplayPriceBreakDown displayPriceBreakDown, String priceBreakupText) {
		if (MapUtils.isNotEmpty(displayPriceBreakDown.getInsuranceBreakupMap())) {
			PricingDetails insurance = new PricingDetails();
			insurance.setKey(Constants.TOTAL_INSURANCE);
			insurance.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.INSURANCE_LABEL));
			if (StringUtils.isNotEmpty(priceBreakupText))
				insurance.setLabel(priceBreakupText);
			insurance.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			List<PricingDetails> breakup = new ArrayList<>();
			double totalAmount = 0.0d;
			for (Map.Entry<String, InsuranceDetails> entry : displayPriceBreakDown.getInsuranceBreakupMap().entrySet()) {
				PricingDetails i = new PricingDetails();
				i.setAmount(entry.getValue().getAmount());
				totalAmount += i.getAmount();
				i.setLabel(entry.getValue().getDisplayLabel());
				i.setKey(Constants.INSURANCE_AMOUNT);
				i.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
				breakup.add(i);
			}
			insurance.setAmount(totalAmount);
			insurance.setBreakup(breakup);
			priceDetails.add(insurance);
		}
	}

	public void buildSMESubscriptionBreakup(List<PricingDetails> priceDetails, DisplayPriceBreakDown displayPriceBreakDown) {
		if (displayPriceBreakDown.getSmeSubscriptionAmount()!=null && displayPriceBreakDown.getSmeSubscriptionAmount()>0.0) {

			PricingDetails smeSubscription = new PricingDetails();

			smeSubscription.setKey(TOTAL_SME);
			smeSubscription.setLabel(polyglotService.getTranslatedData(SME_LABEL));
			smeSubscription.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));

			double totalAmount = 0.0d;
			totalAmount += displayPriceBreakDown.getSmeSubscriptionAmount();
			smeSubscription.setAmount(totalAmount);
			priceDetails.add(smeSubscription);
		}
	}

	public void buildCharityAddonBreakUp(List<PricingDetails> priceDetails, DisplayPriceBreakDown displayPriceBreakDown){
		if(displayPriceBreakDown != null && displayPriceBreakDown.getCharityAmountV2() > 0.0d && displayPriceBreakDown.isCharityV2Enable()){
			PricingDetails charityV2 = new PricingDetails();
			charityV2.setAmount(displayPriceBreakDown.getCharityAmountV2());
			charityV2.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			charityV2.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.DONATION_PRICE_LABLE));
			charityV2.setKey(CHARITY);
			priceDetails.add(charityV2);
		}
	}

	private void buildTotalAmount(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn, String payMode, String controller) {
		PricingDetails totalAmount = new PricingDetails();
		if (utility.isReviewPageAPI(controller) && displayPriceBrkDwn.getFlexiCancellationCharges() > 0d) {
			totalAmount.setAmount(displayPriceBrkDwn.getDisplayPrice() + displayPriceBrkDwn.getFlexiCancellationCharges());
		} else {
			totalAmount.setAmount(displayPriceBrkDwn.getDisplayPrice());
		}
		totalAmount.setKey(Constants.TOTAL_AMOUNT_KEY);
		totalAmount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
		if (Utility.isPahOnlyPaymode(payMode)) {
			totalAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.AMOUNT_YOU_PAYING_AT_HOTEL_LABEL));
		} else {
			totalAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_AMOUNT_LABEL));
		}
		pricingDetails.add(totalAmount);
	}

	/***
	 * add "Payment to be done in ₹" text in TotalPricing>paymentInfoText node
	 * conditions: Indian funnel and international hotel and non pah mode and non inr currency
	 * @param totalPricing node to update paymentInfoText value
	 * @param countryCode country code to check international hotel
	 * @param payMode to check non pah mode
	 * @param currencyCode actual currency code of HotelRate
	 */
	public void buildPaymentInfoTextForAlternateCurrency(TotalPricing totalPricing, String countryCode, String payMode, String currencyCode) {
		String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
		if (totalPricing != null && !Utility.isRegionGccOrKsa(region) && !Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode) && Utility.isPasPayMode(payMode) && !Constants.DEFAULT_CUR_INR.equalsIgnoreCase(currencyCode)) {
			totalPricing.setPaymentInfoText(polyglotService.getTranslatedData(PAYMENT_INR_MSG));
		}
	}

	public List<PersuasionObject> buildPersuasions(HotelRates hotelRates, String deviceType){
		Map<String, List<String>> persuasionOrder = null;
		try {
			persuasionOrder = gson.fromJson(detailPagePersuasionOrder, new TypeToken<Map<String, List<String>>>() {
			}.getType());
			List<PersuasionObject> persuasionsList = new ArrayList<>();
			PersuasionObject persuasionObject = new PersuasionObject();
			if(CLIENT_DESKTOP.equalsIgnoreCase(deviceType)){
				persuasionObject.setTemplate("IMAGE_TEXT_DEAL");
				persuasionObject.setPlaceholder("TOP_RIGHT");
			} else {
				persuasionObject.setTemplate("MULTI_PERSUASION_CAROUSEL");
				persuasionObject.setPlaceholder("PLACEHOLDER_CARD_M1");
			}
			PersuasionStyle style = new PersuasionStyle();
			style.setBorderColor("#e5e5e5");
			style.setBorderSize("1");
			style.setCornerRadii("8");
			persuasionObject.setStyle(style);

			List<com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData> data = new ArrayList<>();
			Map<String, com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData> persuasions = buildPersuasionsData(hotelRates, deviceType);
			if (persuasions != null) {
				List<String> order = Utility.isGCC() ? persuasionOrder.get(GCC) : persuasionOrder.get(B2C);
				for (String persuasionType : order) {
					if (persuasions.containsKey(persuasionType)) {
						data.add(persuasions.get(persuasionType));
					}
				}
			}

			persuasionObject.setData(data);
			persuasionsList.add(persuasionObject);
			return CollectionUtils.isNotEmpty(data) ? persuasionsList : null;

		} catch(Exception e) {
			logger.error("Error in getting persuasion order ", e);
		}
		return null;
	}



	public Map<String, com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData> buildPersuasionsData(HotelRates hotelRates, String deviceType) {
		Map<String, com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData> persuasions = new HashMap<>();
		PersuasionStyle persuasionChildStyle = new PersuasionStyle();
		persuasionChildStyle.setTextColor("#757575");
		persuasionChildStyle.setIconHeight(6);
		persuasionChildStyle.setIconWidth(6);
		persuasionChildStyle.setImageHeight("24");
		persuasionChildStyle.setImageWidth("180");

		// only today deal
		if(hotelRates != null && hotelRates.getDealInfoList() != null && CollectionUtils.isNotEmpty(hotelRates.getDealInfoList())) {
			DealInfo dealInfo = hotelRates.getDealInfoList().get(0);
			if (dealInfo != null) {
				PersuasionData dealPersuasion = new PersuasionData();
				dealPersuasion.setStyle(persuasionChildStyle);
				dealPersuasion.setType(onlyTodayDealPersuasionType);
				if (dealInfo.getTitle() != null) {
					PersuasionTitle title = new PersuasionTitle();
					title.setText(dealInfo.getTitle());
					PersuasionStyle titleStyle = new PersuasionStyle();
					titleStyle.setTextColor("#007E7D");
					title.setStyle(titleStyle);
					dealPersuasion.setPersuasionTitle(title);
				}
				if (dealInfo.getSubTitle() != null) {
					dealPersuasion.setPersuasionText(dealInfo.getSubTitle());
				}
				if (dealInfo.getTimer() != null) {
					PersuasionTimer timer = new PersuasionTimer();
					timer.setExpiry(dealInfo.getTimer().getExpiry());
					timer.setStyle(onlyTodayDealTimerStyle);
					dealPersuasion.setTimer(timer);
				}
				persuasions.put(onlyTodayDealPersuasionType, dealPersuasion);
			}
		}

		// exclusive

		if(!CLIENT_DESKTOP.equalsIgnoreCase(deviceType)) {
			if (hotelRates.isUserGCCAndMmtExclusive()) {
				com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData mmtExclusive = new com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData();
				mmtExclusive.setImageUrl(gccImages.get("exclusiveImageUrl"));
				mmtExclusive.setIconurl(gccImages.get("exclusiveIconUrl"));
				mmtExclusive.setType(MMT_EXCLUSIVE_TYPE);
				mmtExclusive.setStyle(persuasionChildStyle);
				List<String> inclusions = new ArrayList<>();
				inclusions.add(polyglotService.getTranslatedData(MMT_EXCLUSIVE_INCLUSIONS_1));
				mmtExclusive.setInclusions(inclusions);
				persuasions.put(MMT_EXCLUSIVE_TYPE, mmtExclusive);
			}


			//Black
			if (hotelRates.getBlackInfo() != null && CollectionUtils.isNotEmpty(hotelRates.getBlackInfo().getInclusionsList())) {
				com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData persuasionBlackInfoData = new com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData();
				BlackInfo blackInfo = buildBlackInfo(hotelRates.getBlackInfo());
				if (blackInfo != null) {
					persuasionBlackInfoData.setIconurl(Utility.isGCC() ? gccImages.get("selectIconUrl") : blackInfo.getTitleImageUrl());
					persuasionBlackInfoData.setImageUrl(Utility.isGCC() ? gccImages.get("selectImageUrl") : blackInfo.getIconUrl());
					persuasionBlackInfoData.setInclusions(buildInclusionListOld(blackInfo.getInclusionsList()));
					persuasionBlackInfoData.setStyle(persuasionChildStyle);
					persuasionBlackInfoData.setType(blackPersuasionType);
					if (CollectionUtils.isNotEmpty(persuasionBlackInfoData.getInclusions())) {
						persuasions.put(blackPersuasionType, persuasionBlackInfoData);
					}
				}

			}

			//LOS
			if (hotelRates.getLongStayBenefits() != null && CollectionUtils.isNotEmpty(hotelRates.getLongStayBenefits().getInclusionsList())) {
				com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData persuasionLOSData = new com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData();
				persuasionLOSData.setImageUrl(LOS_BENEFIT_IMAGE_URL); //Have to take from backend
				persuasionLOSData.setIconurl(LOS_BENEFIT_ICON_URL); // Have to take from backend
				//persuasionLOSData.setPersuasionText(hotelRates.getLongStayBenefits().getTitle());
				persuasionLOSData.setInclusions(buildInclusionListOld(hotelRates.getLongStayBenefits().getInclusionsList()));
				persuasionLOSData.setStyle(persuasionChildStyle);
				persuasionLOSData.setType(losPersuasionType);
				if (CollectionUtils.isNotEmpty(persuasionLOSData.getInclusions())) {
					persuasions.put(losPersuasionType, persuasionLOSData);
				}
			}

			//Occassions
			if (hotelRates.getOccassionPackageRoomDetails() != null && hotelRates.getOccassionPackageRoomDetails().getOccassionDetails() != null) {
				com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData persuasionOccassionData = new com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData();
				persuasionOccassionData.setImageUrl(hotelRates.getOccassionPackageRoomDetails().getOccassionDetails().getPersuasionImageUrl()); //Have to take from backend
				persuasionOccassionData.setIconurl(hotelRates.getOccassionPackageRoomDetails().getOccassionDetails().getPersuasionIconUrl()); // Have to take from backend
				persuasionOccassionData.setPersuasionText(hotelRates.getOccassionPackageRoomDetails().getOccassionDetails().getPersuasionText());
				persuasionOccassionData.setInclusions(hotelRates.getOccassionPackageRoomDetails().getOccassionDetails().getInclusions());
				persuasionOccassionData.setStyle(persuasionChildStyle);
				persuasionOccassionData.setType(hotelRates.getOccassionPackageRoomDetails().getOccassionDetails().getOccassionType());
				if (CollectionUtils.isNotEmpty(persuasionOccassionData.getInclusions())) {
					persuasions.put(superPackagePersuasionType, persuasionOccassionData);
				}
			} else if (hotelRates.getPackageRoomDetails() != null && hotelRates.getPackageRoomDetails().getOccassionDetails() != null) {
				com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData persuasionPackageData = new com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData();
				persuasionPackageData.setImageUrl(hotelRates.getPackageRoomDetails().getOccassionDetails().getPersuasionImageUrl()); //Have to take from backend
				persuasionPackageData.setIconurl(hotelRates.getPackageRoomDetails().getOccassionDetails().getPersuasionIconUrl()); // Have to take from backend
				persuasionPackageData.setPersuasionText(hotelRates.getPackageRoomDetails().getOccassionDetails().getPersuasionText());
				persuasionPackageData.setInclusions(hotelRates.getPackageRoomDetails().getOccassionDetails().getInclusions());
				persuasionPackageData.setStyle(persuasionChildStyle);
				persuasionPackageData.setType(superPackagePersuasionType);
				if (CollectionUtils.isNotEmpty(persuasionPackageData.getInclusions())) {
					persuasions.put(superPackagePersuasionType, persuasionPackageData);
				}

			}
			//Discount has no inclusion
			if (hotelRates.getAppliedOffers() != null && CollectionUtils.isNotEmpty(hotelRates.getAppliedOffers())) {
				com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData persuasionDiscountData = new com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData();
				persuasionDiscountData.setIconurl(hotelRates.getAppliedOffers().get(0).getIconUrl());
				//persuasionDiscountData.setImageUrl();// Have to take from backend
				persuasionDiscountData.setPersuasionText(hotelRates.getAppliedOffers().get(0).getLandingPersuasionText());
				persuasionDiscountData.setStyle(persuasionChildStyle);
				persuasionDiscountData.setType(discountPersuasionType);
				persuasions.put(discountPersuasionType, persuasionDiscountData);
			}
		}
		return persuasions;
	}

	private List<String> buildInclusionList(List<Inclusion> inclusions) {
		List<String> inclusionList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(inclusions)) {
			for (Inclusion inclusion : inclusions) {
				inclusionList.add(inclusion.getValue());
			}
		}
		return inclusionList;

	}

	public void updateTotalAmountInHotelierCurrency(List<PricingDetails> pricingDetails, String payMode, String userCurrency, String supplierCurrency, double convFactor) {

		String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
		if (Utility.isPahOnlyPaymode(payMode) && !userCurrency.equalsIgnoreCase(supplierCurrency) && !Utility.isRegionGccOrKsa(region)) {
			for (PricingDetails priceDetail : pricingDetails) {
				if (Constants.TOTAL_AMOUNT_KEY.equalsIgnoreCase(priceDetail.getKey())) {
					priceDetail.setHotelierCurrencyCode(supplierCurrency);
					priceDetail.setHotelierCurrencyAmount(Utility.round(priceDetail.getAmount() * convFactor, 2));
					break;
				}
			}
		}
	}

	private List<String> buildInclusionListOld(List<Inclusion> inclusions) {
		List<String> inclusionList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(inclusions)) {
			for (Inclusion inclusion : inclusions) {
				if(inclusion.getCode() != null) {
					inclusionList.add(inclusion.getCode());
				}
			}
		}
		return inclusionList;
	}

	private void buildTaxesAndServiceFee(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn, String countryCode, String expData, boolean cbrAvailable) {
		double totalTaxesAndSrvcFee = 0.0d;
		List<PricingDetails> taxesBreakup = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(displayPriceBrkDwn.getTaxBreakupList())) {
			int counter = 0;
			for (TaxBreakup taxBreakup : displayPriceBrkDwn.getTaxBreakupList()) {
				PricingDetails taxPricingDetail = new PricingDetails();
				taxPricingDetail.setAmount(taxBreakup.getAmount());
				taxPricingDetail.setKey(new StringBuilder(Constants.TAX_BREAKUP_KEY).append(String.valueOf(counter++)).toString());
				taxPricingDetail.setLabel(taxBreakup.getType());
				taxPricingDetail.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
				taxesBreakup.add(taxPricingDetail);
				totalTaxesAndSrvcFee += taxPricingDetail.getAmount();
			}
		} else if (displayPriceBrkDwn.getHotelTax() > 0.0d) {
			double hotelTaxAmount = displayPriceBrkDwn.getHotelTax();
			if (displayPriceBrkDwn.getHotelServiceCharge() > 0.0d) {
				hotelTaxAmount -= displayPriceBrkDwn.getHotelServiceCharge();
				PricingDetails serviceCharge = new PricingDetails();
				serviceCharge.setAmount(displayPriceBrkDwn.getHotelServiceCharge());
				serviceCharge.setKey(Constants.SERVICE_CHARGE_KEY);
				serviceCharge.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_CHARGE_LABEL));
				serviceCharge.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
				taxesBreakup.add(serviceCharge);
				totalTaxesAndSrvcFee += serviceCharge.getAmount();
			}
			PricingDetails hotelTax = new PricingDetails();
			hotelTax.setAmount(hotelTaxAmount);
			hotelTax.setKey(Constants.HOTEL_TAX_KEY);
			hotelTax.setLabel(Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode) ? polyglotService.getTranslatedData(ConstantsTranslation.GST_LABEL) : polyglotService.getTranslatedData(ConstantsTranslation.HOTEL_TAX_LABEL));
			hotelTax.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			taxesBreakup.add(hotelTax);
			totalTaxesAndSrvcFee += hotelTax.getAmount();
		}
		if (displayPriceBrkDwn.getMmtServiceCharge() > 0.0d) {
			PricingDetails serviceFee = new PricingDetails();
			serviceFee.setAmount(displayPriceBrkDwn.getMmtServiceCharge());
			serviceFee.setKey(Constants.SERVICE_FEES_KEY);
			serviceFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_FEES_LABEL));
			serviceFee.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			taxesBreakup.add(serviceFee);
			totalTaxesAndSrvcFee += serviceFee.getAmount();

			if(displayPriceBrkDwn.isServiceFeeGstIncluded()){
				PricingDetails serviceFeeGST = new PricingDetails();
				long gstFees = Math.round((displayPriceBrkDwn.getMmtServiceCharge() * commonConfigConsul.getServiceFeeGstPercentage())/100);
				serviceFeeGST.setAmount(gstFees);
				serviceFeeGST.setKey(Constants.SERVICE_FEES_GST_KEY);
				serviceFeeGST.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_FEES_GST_LABEL));
				serviceFeeGST.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
				taxesBreakup.add(serviceFeeGST);
			}

		}
		if (displayPriceBrkDwn.getAffiliateFee() > 0.0d) {
			PricingDetails affiliateFee = new PricingDetails();
			affiliateFee.setAmount(displayPriceBrkDwn.getAffiliateFee());
			affiliateFee.setKey(Constants.AFFILIATE_FEES_KEY);
			affiliateFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.AFFILIATE_FEES_LABEL));
			affiliateFee.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			taxesBreakup.add(affiliateFee);
			totalTaxesAndSrvcFee += affiliateFee.getAmount();
		}
		if (totalTaxesAndSrvcFee > 0.0d) {
			PricingDetails taxesAndSrvcFee = new PricingDetails();
			taxesAndSrvcFee.setAmount(totalTaxesAndSrvcFee);
			taxesAndSrvcFee.setKey(Constants.TAXES_KEY);
			//Excluded Charges text to be shown above taxes for GCC on review page
			if (Utility.isGccOrKsa() && utility.isExperimentOn(utility.getExpDataMap(expData), GEC)) {
				taxesAndSrvcFee.setSubLine(polyglotService.getTranslatedData(ConstantsTranslation.TAXES_EXCLUDED_TEXT_REVIEW_PAGE));
			}
			String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
			if ((ControllerConstants.LISTING_SEARCH_HOTELS.equalsIgnoreCase(controller) || ControllerConstants.DETAIL_SEARCH_ROOMS.equalsIgnoreCase(controller))
					&& (displayPriceBrkDwn.getHotelTax() > 0.0d || displayPriceBrkDwn.getMmtServiceCharge() > 0.0d)) {
				taxesAndSrvcFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TAXES_LISTING_DETAIL_LABEL));
			} else if (displayPriceBrkDwn.getHotelTax() > 0.0d && displayPriceBrkDwn.getMmtServiceCharge() > 0.0d) {
				taxesAndSrvcFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TAXES_LABEL)); // if taxes and service fee both there
			} else if (displayPriceBrkDwn.getHotelTax() > 0.0d) {
				taxesAndSrvcFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.HOTEL_TAX_LABEL)); // if only hotel taxes
			} else {
				taxesAndSrvcFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_FEES_LABEL)); // only service fees
			}
			taxesAndSrvcFee.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			if (!cbrAvailable) // not to send the break-up of taxes to client if cheapest buy rate is true for the ratePlan selected
				taxesAndSrvcFee.setBreakup(taxesBreakup);
			pricingDetails.add(taxesAndSrvcFee);
		}
	}

//    private void buildAffiliateFee(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
//    	if(displayPriceBrkDwn.getAffiliateFee() > 0.0d) {
//    		PricingDetails affiliateFee = new PricingDetails();
//    		affiliateFee.setAmount(displayPriceBrkDwn.getAffiliateFee());
//    		affiliateFee.setKey(Constants.AFFILIATE_FEES_KEY);
//    		affiliateFee.setLabel(Constants.AFFILIATE_FEES_LABEL);
//    		affiliateFee.setType(Constants.PRICE_TYPE_SUM);
//    		pricingDetails.add(affiliateFee);
//    	}
//    }

	private void buildWallet(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
		if (displayPriceBrkDwn.getWallet() > 0.0d) {
			PricingDetails wallet = new PricingDetails();
			wallet.setAmount(displayPriceBrkDwn.getWallet());
			wallet.setKey(Constants.WALLET_KEY);
			wallet.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.WALLET_LABEL));
			wallet.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_DIFF));
			pricingDetails.add(wallet);
		}
	}

	private void buildPriceAfterDiscount(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
		double totalDiscountAmount = displayPriceBrkDwn.getMmtDiscount() + displayPriceBrkDwn.getBlackDiscount() + displayPriceBrkDwn.getLosBenefitsDiscount() + displayPriceBrkDwn.getCdfDiscount() + displayPriceBrkDwn.getWallet();
		if (totalDiscountAmount > 0.0d) {
			double priceAfterDiscountAmount = displayPriceBrkDwn.getBasePrice() - totalDiscountAmount;
			PricingDetails priceAfterDiscount = new PricingDetails();
			priceAfterDiscount.setAmount(priceAfterDiscountAmount);
			priceAfterDiscount.setKey(Constants.PRICE_AFTER_DISCOUNT_KEY);
			priceAfterDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_AFTER_DISCOUNT_LABEL));
			priceAfterDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			pricingDetails.add(priceAfterDiscount);
		}
	}

	private void buildTotalDiscounts(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn, boolean isCorp, String segmentId, String expData, String extraDiscountType, boolean isMetaTraffic, boolean isNewPropertyOfferApplicable) {
		double totalDiscountAmount = 0.0d;
		double totalSupplierOfferDiscount = 0.0d;
		String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());

		List<PricingDetails> priceBreakup = new ArrayList<>();

		/* Build SupplierDiscount for Pricer breakup if available = right now only Last_Minute or Early_Bird configure on supplierDealMap */
		if (MapUtils.isNotEmpty(displayPriceBrkDwn.getSupplierDealsDetailMap()) && displayPriceBrkDwn.getSupplierDealsDetailMap().containsKey(DEAL)) {
			Tuple<String,Double> supplierDiscount = getSupplierDealAmountAndName(displayPriceBrkDwn.getSupplierDealsDetailMap(), isNewPropertyOfferApplicable);
			if (supplierDiscount.getY() > 0.0d && StringUtils.isNotBlank(supplierDiscount.getX())) {
				PricingDetails pd = new PricingDetails();
				pd.setAmount(supplierDiscount.getY());
				pd.setLabel(polyglotService.getTranslatedData(supplierDiscount.getX().concat("_DISC_LABEL")));
				pd.setKey(supplierDiscount.getX());
				pd.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
				totalSupplierOfferDiscount = totalSupplierOfferDiscount + supplierDiscount.getY();
				priceBreakup.add(pd);
			}
			totalDiscountAmount = totalDiscountAmount + totalSupplierOfferDiscount;

		}
		/* Build OfferDetailMap from Pricer breakup if available */

		if (displayPriceBrkDwn.getMmtDiscount() - totalSupplierOfferDiscount > 0.0d) {
			PricingDetails mmtDiscount = new PricingDetails();
			mmtDiscount.setAmount(displayPriceBrkDwn.getMmtDiscount() - totalSupplierOfferDiscount);
			mmtDiscount.setKey(Constants.MMT_DISCOUNT_KEY);
			if(isMetaTraffic) {
				mmtDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.META_DISCOUNT_LABEL));
			}
			else{
				mmtDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.HOTELIER_DISCOUNT_LABEL));
			}
			mmtDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			if (isCorp && corpSegments.contains(segmentId)) {
				mmtDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.HOTELIER_DISCOUNT_LABEL_CORP));
			}
			priceBreakup.add(mmtDiscount);
			totalDiscountAmount += mmtDiscount.getAmount();
		}

		if (displayPriceBrkDwn.getBlackDiscount() > 0.0d) {
			PricingDetails blackDiscount = new PricingDetails();
			blackDiscount.setAmount(displayPriceBrkDwn.getBlackDiscount());
			blackDiscount.setKey(Constants.BLACK_DISCOUNT_KEY);
			// For GCC, MMT_SELECT Program runs hence picking Select Label in this case
			// For IN, MMT_BLACK Program runs hence picking Black label in this case
			if (Utility.isGccOrKsa()) {
				blackDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.GCC_SELECT_DISCOUNT_LABEL));
			} else {
				blackDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.BLACK_DISCOUNT_LABEL));
			}
			blackDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			priceBreakup.add(blackDiscount);
			totalDiscountAmount += blackDiscount.getAmount();
		}

		if (displayPriceBrkDwn.getLosBenefitsDiscount() > 0.0d) {
			PricingDetails losBenefitsDiscount = new PricingDetails();
			losBenefitsDiscount.setAmount(displayPriceBrkDwn.getLosBenefitsDiscount());
			losBenefitsDiscount.setKey(Constants.LONGSTAY_BENEFITS);
			losBenefitsDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.LOS_DISCOUNT_LABEL));
			losBenefitsDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			priceBreakup.add(losBenefitsDiscount);
			totalDiscountAmount += losBenefitsDiscount.getAmount();
		}

		if (displayPriceBrkDwn.getCdfDiscount() > 0.0d) {
			PricingDetails cdfDiscount = new PricingDetails();
			cdfDiscount.setAmount(displayPriceBrkDwn.getCdfDiscount());
			cdfDiscount.setKey(Constants.CDF_DISCOUNT_KEY);
			cdfDiscount.setLabel(displayPriceBrkDwn.getCouponInfo() != null ? displayPriceBrkDwn.getCouponInfo().getCouponCode() : polyglotService.getTranslatedData(ConstantsTranslation.CDF_DISCOUNT_LABEL));
			cdfDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			if (displayPriceBrkDwn.getMmtServiceCharge() > 0.0d && displayPriceBrkDwn.getCdfDiscount() > displayPriceBrkDwn.getMmtServiceCharge()) {
				List<PricingDetails> cdfCouponBreakup = new ArrayList<>();
				PricingDetails serviceFeeReversal = new PricingDetails();
				serviceFeeReversal.setKey(Constants.SERVICE_FEES_REVERSAL_KEY);
				serviceFeeReversal.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_FEES_REVERSAL_LABLE));
				serviceFeeReversal.setAmount(displayPriceBrkDwn.getMmtServiceCharge());
				serviceFeeReversal.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
				cdfCouponBreakup.add(serviceFeeReversal);
				PricingDetails effectiveCouponApplied = new PricingDetails();
				effectiveCouponApplied.setKey(Constants.EFFECTIVE_COUPON_APPLIED_KEY);
				effectiveCouponApplied.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.EFFECTIVE_COUPON_APPLIED_LABLE));
				effectiveCouponApplied.setAmount(displayPriceBrkDwn.getCdfDiscount() - displayPriceBrkDwn.getMmtServiceCharge());
				effectiveCouponApplied.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
				cdfCouponBreakup.add(effectiveCouponApplied);
				cdfDiscount.setBreakup(cdfCouponBreakup);
			}
			priceBreakup.add(cdfDiscount);
			totalDiscountAmount += cdfDiscount.getAmount();
		}
		if (displayPriceBrkDwn.getWallet() > 0.0d) {
			PricingDetails wallet = new PricingDetails();
			wallet.setAmount(displayPriceBrkDwn.getWallet());
			wallet.setKey(Constants.WALLET_KEY);
			wallet.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.PROMO_CASH));
			wallet.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			priceBreakup.add(wallet);
			totalDiscountAmount += wallet.getAmount();
		}

		if (totalDiscountAmount > 0.0d) {
			PricingDetails totalDiscount = new PricingDetails();
			totalDiscount.setAmount(totalDiscountAmount);
			totalDiscount.setKey(Constants.TOTAL_DISCOUNT_KEY);
			totalDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_DISCOUNT_LABEL));
			totalDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_DIFF));
			totalDiscount.setBreakup(priceBreakup);
			if (displayPriceBrkDwn!=null && displayPriceBrkDwn.getCouponInfo()!=null && utility.isReviewPageAPI(controller)) {
				totalDiscount.setCouponPersuasion(buildCouponPersuasion(extraDiscountType, displayPriceBrkDwn.getCouponInfo().getDiscountAmount(), displayPriceBrkDwn.getDiscountPersuasionInfo(), isNewPropertyOfferApplicable));
			}
			pricingDetails.add(totalDiscount);
		}
	}

	private Tuple<String,Double> getSupplierDealAmountAndName(Map<String,String> supplierDealMap, boolean isNewPropertyOfferApplicable) {
		double amount = 0.0;
		String deal = StringUtils.EMPTY;
		if (supplierDealMap.get(DEAL).equalsIgnoreCase(PromotionalOfferType.LAST_MINUTE.name())) {
			try {
				amount = amount + Double.parseDouble(supplierDealMap.get(DISCOUNT));
				deal = PromotionalOfferType.LAST_MINUTE.getName();
			} catch (Exception e) {
				// Do nothing for now
			}
		}
		if (supplierDealMap.get(DEAL).equalsIgnoreCase(PromotionalOfferType.EARLY_BIRD.name())) {
			try {
				amount = Double.parseDouble(supplierDealMap.get(DISCOUNT));
				deal = PromotionalOfferType.EARLY_BIRD.getName();
			} catch (Exception e) {
				// Do nothing for now
			}
		}
		if (isNewPropertyOfferApplicable && supplierDealMap.get(DEAL).equalsIgnoreCase(PromotionalOfferType.FBP.name())) {
			try {
				amount = Double.parseDouble(supplierDealMap.get(DISCOUNT));
				deal = PromotionalOfferType.FBP.getName();
			} catch (Exception e) {
				// Do nothing for now
			}
		}
		return new Tuple<>(deal,amount);
	}

	public CancellationTimeline buildCancellationTimeline(com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTL, BNPLVariant bnplVariant) {
		if (cancellationTL != null) {
			CancellationTimeline cancellationTimeline = new CancellationTimeline();
			cancellationTimeline.setBookingDate(cancellationTL.getBookingDate());
			cancellationTimeline.setCancellationDate(cancellationTL.getCancellationDate());
			cancellationTimeline.setCancellationDateTime(cancellationTL.getCancellationDateTime());
			cancellationTimeline.setCardChargeDate(cancellationTL.getCardChargeDate());
			cancellationTimeline.setCardChargeDateTime(cancellationTL.getCardChargeDateTime());
			cancellationTimeline.setDateFormat(cancellationTL.getDateFormat());
			cancellationTimeline.setCardChargeText(cancellationTL.getCardChargeText());
			cancellationTimeline.setBookingAmountText(cancellationTL.getBookingAmountText());
			cancellationTimeline.setCheckInDate(cancellationTL.getCheckInDate());
			cancellationTimeline.setCheckInDateTime(cancellationTL.getCheckInDateTime());
			cancellationTimeline.setFreeCancellationText(cancellationTL.getFreeCancellationText());
			cancellationTimeline.setSubTitle(cancellationTL.getSubTitle());
			cancellationTimeline.setTitle(cancellationTL.getTitle());
			cancellationTimeline
					.setFreeCancellationBenefits(buildFreeCancellationBenefits(cancellationTL.getFreeCancellationBenefits(), bnplVariant));
			return cancellationTimeline;
		}
		return null;
	}

	public List<FCBenefit> buildFreeCancellationBenefits(List<com.mmt.hotels.model.response.pricing.FCBenefit> freeCancellationBenefits, BNPLVariant bnplVariant) {
		if (CollectionUtils.isEmpty(freeCancellationBenefits)) {
			return null;
		}
		List<FCBenefit> benefitsForCG = new ArrayList<>();
		boolean allSingleTickIconFCBenefits = true;
		for (com.mmt.hotels.model.response.pricing.FCBenefit benefitHES : freeCancellationBenefits) {
			FCBenefit benefitCG = new FCBenefit();
			benefitCG.setText(benefitHES.getText());
			if(benefitHES.getType().equalsIgnoreCase(BNPL_DISABLED)){
				benefitCG.setIconType(IconType.DEFAULT);
				allSingleTickIconFCBenefits = false;
			}
			else if (benefitHES.getType().equalsIgnoreCase(TYPE_FCZPN)) {
				benefitCG.setIconType(IconType.DOUBLETICK);
				allSingleTickIconFCBenefits = false;
			} else {
				benefitCG.setIconType(IconType.SINGLETICK);
			}
			benefitsForCG.add(benefitCG);
		}

		if(BNPLVariant.BNPL_AT_0.equals(bnplVariant)){
			FCBenefit benefitCG = new FCBenefit();
			benefitCG.setText(polyglotService.getTranslatedData(Constants.PAY_ON_MMT_ONLY_INSTRUCTION));

			if(allSingleTickIconFCBenefits) {
				benefitCG.setIconType(IconType.SINGLETICK);
			}
			else{
				benefitCG.setIconType(IconType.DOUBLETICK);
			}
			benefitsForCG.add(benefitCG);
		}

		return benefitsForCG;
	}

	public BNPLDetails buildBNPLDetails(boolean bnplApplicable, String bnplPersuasionMsg, String bnplPolicyText, String bnplNewVariantText,
										String bnplNewVariantSubText, boolean hotelOriginalBNPL, boolean showBnplCard, BNPLVariant bnplVariant, Double bnplFinalPrice) {
		if (checkBnplApplicability(bnplApplicable, hotelOriginalBNPL, showBnplCard)) {
			BNPLDetails bnplDetails = new BNPLDetails();
			bnplDetails.setBnplApplicable(bnplApplicable);
			bnplDetails.setBnplPersuasionMsg(bnplPersuasionMsg);
			bnplDetails.setBnplPolicyText(bnplPolicyText);
			if(bnplFinalPrice!=null && (int) Math.round(bnplFinalPrice)!=0) {
				bnplDetails.setFinalPrice((int) Math.round(bnplFinalPrice));
			}
			if (!Utility.isGccOrKsa())
				bnplDetails.setBnplVariant(bnplVariant != null ? bnplVariant.name() : null);
			setBnplNewVariantDetails(bnplNewVariantText, bnplNewVariantSubText, bnplDetails);
			return bnplDetails;
		}
		return null;
	}

	public BNPLDetails buildBNPLDetailsForDisabledBnpl(BNPLDisabledReason reasonForDisabling, String nonBnplCouponAppliedCode, BNPLVariant bnplVariant, Integer bnplActiveBookingCount, int bnplAllowedCount) {
		if (reasonForDisabling == null) return null;
		BNPLDetails bnplDetails = new BNPLDetails();
		bnplDetails.setBnplApplicable(false);
		switch (reasonForDisabling) {
			case ACTIVE_BOOKINGS_THRESHOLD:
				bnplDetails.setBnplNewVariantText(polyglotService.getTranslatedData(BNPLVariant.BNPL_AT_1.equals(bnplVariant) ? BNPL_BOOKINGS_THRESHOLD_REACHED_TEXT : BNPL0_BOOKINGS_THRESHOLD_REACHED_TEXT));
				if (bnplActiveBookingCount != null) {
					//count of bookings to complete to be eligible for BNPL
					int bookingToCompleteCount = bnplActiveBookingCount - bnplAllowedCount + 1;
					String activeBookingThresholdText = polyglotService.getTranslatedData(bookingToCompleteCount == 1 ? BNPL_SINGLE_BOOKING_THRESHOLD_SUBTEXT : BNPL_MULTIPLE_BOOKING_THRESHOLD_SUBTEXT);
					bnplDetails.setBnplNewVariantSubText(activeBookingThresholdText.replace("{active_count}", bnplActiveBookingCount.toString()).replace("{cancel_count}", String.valueOf(bookingToCompleteCount)));
				}
				break;
			case NON_BNPL_COUPON_APPLIED:
				bnplDetails.setBnplNewVariantText(polyglotService.getTranslatedData(BNPLVariant.BNPL_AT_1.equals(bnplVariant) ? NON_BNPL_COUPON_APPLIED_TEXT : BNPL0_BOOKINGS_THRESHOLD_REACHED_TEXT));
				bnplDetails.setBnplNewVariantSubText(polyglotService.getTranslatedData(NON_BNPL_COUPON_APPLIED_SUBTEXT).replace("{coupon_code}", String.valueOf(nonBnplCouponAppliedCode)));
				break;
			case INSURANCE_APPLIED:
				bnplDetails.setBnplNewVariantText(polyglotService.getTranslatedData(BNPLVariant.BNPL_AT_1.equals(bnplVariant) ? INSURANCE_BNPL1_TEXT : INSURANCE_BNPL0_TEXT));
				bnplDetails.setBnplNewVariantSubText(polyglotService.getTranslatedData(INSURANCE_BNPL_SUBTEXT));
				break;
		}
		return bnplDetails;
	}

	/* bnplDetails node is there in response based upon showBnplCard flag, for only GCC funnel. */
	public boolean checkBnplApplicability(boolean bnplApplicable, boolean hotelOriginalBNPL, boolean showBnplCard) {
		return bnplApplicable || (Utility.isGccOrKsa() && showBnplCard && hotelOriginalBNPL);
	}

	private void setBnplNewVariantDetails(String bnplNewVariantText, String bnplNewVariantSubText, BNPLDetails bnplDetails) {
		if (StringUtils.isNotBlank(bnplNewVariantText)) {
			bnplDetails.setBnplNewVariantText(bnplNewVariantText);
		}
		if (StringUtils.isNotBlank(bnplNewVariantSubText)) {
			bnplDetails.setBnplNewVariantSubText(bnplNewVariantSubText);
		}
	}

	public List<AffiliateFeeDetail> buildAffiliateFeeDetails(List<AffiliateFeeDetails> affiliateFeeDetailsList) {
		if (CollectionUtils.isEmpty(affiliateFeeDetailsList))
			return null;
		List<AffiliateFeeDetail> affiliateFeeDetails = new ArrayList<>();
		for (AffiliateFeeDetails affiliateFeeHES : affiliateFeeDetailsList) {
			AffiliateFeeDetail affiliateFeeDetailCG = new AffiliateFeeDetail();
			BeanUtils.copyProperties(affiliateFeeHES, affiliateFeeDetailCG);
			affiliateFeeDetails.add(affiliateFeeDetailCG);
		}
		return affiliateFeeDetails;
	}

	public List<AddOnNode> getAddons(List<com.mmt.hotels.model.response.addon.AddOnNode> addOns) {
		if (CollectionUtils.isNotEmpty(addOns)) {
			List<AddOnNode> addOnList = new ArrayList<>();
			for (com.mmt.hotels.model.response.addon.AddOnNode addOn : addOns) {
				AddOnNode addOnNode = new AddOnNode();
				addOnNode.setAddOnType(addOn.getAddOnType());
				addOnNode.setId(addOn.getId());
				addOnNode.setProductId(addOn.getProductId());
				addOnNode.setRpMultiplier(addOn.getRpMultiplier());
				addOnNode.setCategory(addOn.getCategory());
				addOnNode.setBucketId(addOn.getBucketId());
				addOnNode.setDescription(addOn.getDescription());
				addOnNode.setBgColor(addOn.getBgColor());
				addOnNode.setCtaText(addOn.getCtaText());
				addOnNode.setItems(addOn.getItems());
				addOnNode.setImageUrl(addOn.getImageUrl());
				addOnNode.setEssenceList(addOn.getEssenceList());
				addOnNode.setPaymentMode(addOn.getPaymentMode());
				addOnNode.setValidFrom(addOn.getValidFrom());
				addOnNode.setInclusion(addOn.getInclusion());
				addOnNode.setExpiry(addOn.getExpiry());
				addOnNode.setAvailableUnits(addOn.getAvailableUnits());
				addOnNode.setInsuranceData(buildInsuranceAddOnData(addOn.getInsuranceData(), addOn.getInsuarnceWidgetData()));
				addOnNode.setMobiusAddOn(addOn.isMobiusAddOn());
				addOnNode.setRefundPolicy(addOn.getRefundPolicy());
				addOnNode.setImageMap(addOn.getImageMap());
				addOnNode.setPrice(addOn.getPrice());
				addOnNode.setAlternateCurrencyPrice(addOn.getAlternateCurrencyPrice());
				if (Constants.EARLY_CHECKIN_CATEGORY.equalsIgnoreCase(addOn.getCategory())) {
					addOnNode.setTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.EARLY_CHECKIN_TITLE), addOn.getPrice()));
				} else {
					addOnNode.setTitle(addOn.getTitle());
				}
				addOnNode.setTncUrl(addOn.getTncUrl());
				addOnNode.setTnc(getTnC(addOn.getTnc()));
				addOnNode.setDescriptions(getAddonDescriptions(addOn.getDescriptions()));
				addOnNode.setAutoSelect(addOn.isAutoSelect());
				addOnNode.setSubscriptionCardData(addOn.getSubscriptionCardData());
				addOnNode.setExpandedViewData(addOn.getExpandedViewData());
				addOnList.add(addOnNode);
			}
			return addOnList;
		}
		return null;
	}

	public String getPriceBreakupTextFromInsuranceAddon(List<com.mmt.hotels.model.response.addon.AddOnNode> addOns) {
		if (CollectionUtils.isNotEmpty(addOns)) {
			for (com.mmt.hotels.model.response.addon.AddOnNode addOn : addOns) {
				String priceBreakupText = getPriceBreakupText(addOn.getInsuranceData());
				if (StringUtils.isNotEmpty(priceBreakupText))
					return priceBreakupText;
			}
		}
		return null;
	}

	//Preparing BHF response node received from HES
	public List<BhfPersuasion> getBhfPersuasions(List<com.mmt.hotels.pojo.response.detail.BhfPersuasion> bhfPersuasions) {
		if (CollectionUtils.isNotEmpty(bhfPersuasions)) {
			List<BhfPersuasion> bhfPersuasionList = new ArrayList<>();
			for (com.mmt.hotels.pojo.response.detail.BhfPersuasion bhfPersuasion : bhfPersuasions) {
				BhfPersuasion bhfPersuasionNode = new BhfPersuasion();
				bhfPersuasionNode.setName(bhfPersuasion.getName());
				bhfPersuasionNode.setTextColor(bhfPersuasion.getTextColor());
				bhfPersuasionNode.setIcon(bhfPersuasion.getIcon());
				bhfPersuasionNode.setBgColor(bhfPersuasion.getBgColor());
				bhfPersuasionNode.setText(bhfPersuasion.getText());
				bhfPersuasionNode.setHeading(bhfPersuasion.getHeading());
				bhfPersuasionNode.setAdditionalText(bhfPersuasion.getAdditionalText());
				bhfPersuasionNode.setLeftCTA(bhfPersuasion.getLeftCTA());
				bhfPersuasionNode.setRightCTA(bhfPersuasion.getRightCTA());
				bhfPersuasionNode.setAdditionalTextColor(bhfPersuasion.getAdditionalTextColor());
				bhfPersuasionList.add(bhfPersuasionNode);
			}
			return bhfPersuasionList;
		}
		return null;
	}

	private InsuranceAddOnData buildInsuranceAddOnData(InsuranceData dataFromHES, com.mmt.hotels.model.response.addon.WidgetData insuarnceWidgetDataHES) {
		if (dataFromHES == null || (CollectionUtils.isEmpty(dataFromHES.getTmInsuranceAddOns()) && insuarnceWidgetDataHES == null))
			return null;
		InsuranceAddOnData data = new InsuranceAddOnData();
		data.setVendorLogo(dataFromHES.getVendorLogo());
		data.setTopHeading(dataFromHES.getTopHeading());
		data.setPriceBreakupText(dataFromHES.getPriceBreakupText());
		data.setIsMultiSelectable(dataFromHES.getMultiSelectable());
		data.setTmInsuranceAddOns(buildInsuranceAddOns(dataFromHES.getTmInsuranceAddOns()));
		data.setWidgetData(buildWidgetData(insuarnceWidgetDataHES));
		return data;
	}

	private WidgetData buildWidgetData(com.mmt.hotels.model.response.addon.WidgetData widgetData) {
		WidgetData widgetDataCG = null;
		if (widgetData != null && widgetData.getData() != null && widgetData.getUi() != null) {
			widgetDataCG = new WidgetData();
			widgetDataCG.setData(widgetData.getData());
			widgetDataCG.setUi(widgetData.getUi());
		}
		return widgetDataCG;
	}

	private String getPriceBreakupText(InsuranceData dataFromHES) {
		if (dataFromHES == null)
			return null;
		return dataFromHES.getPriceBreakupText();
	}

	private List<TmInsuranceAddOn> buildInsuranceAddOns(List<TmInsuranceAddOns> list) {
		if (CollectionUtils.isEmpty(list))
			return null;
		List<TmInsuranceAddOn> addOnlist = new ArrayList<>();
		for (TmInsuranceAddOns insurance : list) {
			TmInsuranceAddOn addOn = new TmInsuranceAddOn();
			addOn.setId(insurance.getId());
			addOn.setDescription(insurance.getDescription());
			addOn.setCurrency(insurance.getCurrency());
			addOn.setTncText(insurance.getTncText());
			addOn.setTncLink(insurance.getTncLink());
			addOn.setHeading(insurance.getHeading());
			addOn.setSubHeading(insurance.getSubHeading());
			addOn.setShortHeading(insurance.getShortHeading());
			addOn.setShortTextDesc(insurance.getShortTextDesc());
			addOn.setPostAttachMessage(insurance.getPostAttachMessage());
			addOn.setFeatureList(insurance.getFeatureList());
			addOn.setLargeIcon(insurance.getLargeIcon());
			addOn.setName(insurance.getName());
			addOn.setIncludedUnits(insurance.getIncludedUnits());
			addOn.setPriceTagLine(insurance.getPriceTagLine());
			addOn.setVendorNo(insurance.getVendorNo());
			addOn.setVendorLogo(insurance.getVendorLogo());
			addOn.setUnitVendorSgst(insurance.getUnitVendorSgst());
			addOn.setUnitVendorCgst(insurance.getUnitVendorCgst());
			addOn.setUnitVendorIgst(insurance.getUnitVendorIgst());
			addOn.setUnitType(insurance.getUnitType());
			addOn.setUnitPrice(insurance.getUnitPrice());
			addOn.setUnitBasePrice(insurance.getUnitBasePrice());
			addOn.setType(insurance.getType());
			addOn.setTotalPrice(insurance.getTotalPrice());
			addOn.setSumInsured(insurance.getSumInsured());
			addOnlist.add(addOn);
		}
		return addOnlist;
	}

	public SelectedSpecialRequests buildSelctedSpecialRequests(SpecialRequest specialRequestAvailable, SpecialRequest specialRequest) {
		SelectedSpecialRequests selectedSpecialRequest = null;

		Map<String, SpecialRequestCategory> availableSpecialRequests = specialRequestAvailable.getCategories()
				.stream()
				.collect(Collectors.toMap(SpecialRequestCategory::getCode, Function.identity()));

		selectedSpecialRequest = new SelectedSpecialRequests();
		selectedSpecialRequest.setRequests(new ArrayList<>());
		selectedSpecialRequest.setText(polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_REQUEST_TEXT));
		selectedSpecialRequest.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_REQUEST_SUBTEXT));
		for (SpecialRequestCategory selectedCategory : specialRequest.getCategories()) {
			StringBuilder sb = new StringBuilder();
			if ("109".equalsIgnoreCase(selectedCategory.getCode())) {
				if (selectedCategory.getValues() != null && selectedCategory.getValues().length > 0 && StringUtils.isNotBlank(selectedCategory.getValues()[0])) {
					sb.append(selectedCategory.getValues()[0]);
					selectedSpecialRequest.getRequests().add(sb.toString());
				}
				continue;
			}
			if (!availableSpecialRequests.containsKey(selectedCategory.getCode())) {
				logger.warn("Could not find category code {} but was selected while payment checkout. Skipping this category.", selectedCategory.getCode());
				continue;
			}
			sb.append(availableSpecialRequests.get(selectedCategory.getCode()).getName());
			if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(selectedCategory.getSubCategories()) &&
					selectedCategory.getSubCategories().get(0).getValues().length > 0
					&& StringUtils.isNotBlank(selectedCategory.getSubCategories().get(0).getValues()[0])) {
				sb.append(" : ").append(selectedCategory.getSubCategories().get(0).getValues()[0]);
			}
			selectedSpecialRequest.getRequests().add(sb.toString());
		}
		return selectedSpecialRequest;
	}

	private List<AddOnDescriptions> getAddonDescriptions(List<GenericCardPayloadData> descriptions) {
		if (CollectionUtils.isNotEmpty(descriptions)) {
			List<AddOnDescriptions> addonDescriptions = new ArrayList<>();
			for (GenericCardPayloadData desc : descriptions) {
				AddOnDescriptions addonDesc = new AddOnDescriptions();
				addonDesc.setCharityDescription(desc.getCharityDescription());
				addonDesc.setTitleText(desc.getTitleText());
				addonDesc.setItemIconType(desc.getItemIconType());
				addonDesc.setIconUrl(desc.getIconUrl());
				addonDescriptions.add(addonDesc);
			}
			return addonDescriptions;
		}
		return null;
	}

	private List<String> getTnC(Map<String, List<String>> tncMap) {
		if (MapUtils.isNotEmpty(tncMap)) {
			List<String> tncList = new ArrayList<>();
			for (Map.Entry<String, List<String>> entry : tncMap.entrySet()) {
				tncList.addAll(entry.getValue());
			}
		}
		return null;
	}

	public BlackInfo buildBlackInfo(com.mmt.hotels.model.response.mmtprime.BlackInfo bInfo) {
		if (bInfo != null) {
			BlackInfo blackInfo = new BlackInfo();
			blackInfo.setTierName(bInfo.getTierName());
			blackInfo.setTierNumber(bInfo.getTierNumber());
			blackInfo.setIconUrl(bInfo.getIconUrl());
			blackInfo.setMsg(bInfo.getMsg());
			blackInfo.setBorderColor(bInfo.getBorderColour());
			blackInfo.setInclusionsList(bInfo.getInclusionsList());
			blackInfo.setCtaUrl(bInfo.getCtaLink());
			blackInfo.setCtaText(bInfo.getCta());
			blackInfo.setCurrencyIcon(bInfo.getCurrencyIcon());
			blackInfo.setTitle(bInfo.getMsgTitle());
			blackInfo.setCardId(bInfo.getCardId());
			blackInfo.setBgImageUrl(bInfo.getBgImageUrl());
			blackInfo.setCampaignEndTime(bInfo.getCampaignEndTime());
			blackInfo.setMmtSelectPrivilige(bInfo.isMmtSelectPrivilige());
			if (bInfo.isBlackRevamp()) {
				blackInfo.setIconUrl(StringUtils.isNotEmpty(bInfo.getTierHeaderUrl()) ? bInfo.getTierHeaderUrl() : null);
				blackInfo.setBorderGradient(bInfo.getBorderGradient() != null ? BorderGradient.builder()
						.angle(bInfo.getBorderGradient().getAngle())
						.end(bInfo.getBorderGradient().getEnd())
						.start(bInfo.getBorderGradient().getStart())
						.build() : null);
				blackInfo.setTitleImageUrl(bInfo.getTitleImageUrl());
			}
			if (blackInfo.getBorderGradient() == null && bgGradientBlackPopupMap.containsKey(FALLBACK_BLACK_GRADIENT) && bInfo.isBlackRevampFallback()) {
				blackInfo.setIconUrl(detailPageFallbackIconBlack);
				blackInfo.setBorderGradient(BorderGradient.builder()
						.angle(bgGradientBlackPopupMap.get(FALLBACK_BLACK_GRADIENT).getAngle())
						.end(bgGradientBlackPopupMap.get(FALLBACK_BLACK_GRADIENT).getEnd())
						.start(bgGradientBlackPopupMap.get(FALLBACK_BLACK_GRADIENT).getStart())
						.build());
				if (CollectionUtils.isNotEmpty(blackInfo.getInclusionsList())) {
					blackInfo.getInclusionsList().forEach(inclusion -> inclusion.setImageURL(blackRevampFallbackBulletIcon));
				}
			}
			blackInfo.setTierName(updateTierName(bInfo));
			return blackInfo;
		}
		return null;
	}

	public HotelBenefits buildBenefitInfo(com.mmt.hotels.model.response.HotelBenefitInfo benefitInfo) {
		if (benefitInfo != null) {
			HotelBenefits hotelBenefits = new HotelBenefits();
			hotelBenefits.setTitle(benefitInfo.getTitle());
			hotelBenefits.setTitleColor("#000000");
			hotelBenefits.setDescription(benefitInfo.getText());
			hotelBenefits.setDescriptionColor("#4a4a4a");
			hotelBenefits.setBenefitType(benefitInfo.getBenefitType());
			hotelBenefits.setIconUrl(benefitInfo.getIconUrl());
			hotelBenefits.setIconHeight(37);
			hotelBenefits.setIconWidth(41);

			if(hotelBenefits.getStyle()==null) {
				HotelBenefitStyle benefitStyle = new HotelBenefitStyle();
				benefitStyle.setBorderColor("#d8d8d8");
				benefitStyle.setBorderSize("2");
				benefitStyle.setCornerRadii("8");
				hotelBenefits.setStyle(benefitStyle);
			}
			return hotelBenefits;
		}
		return null;
	}

	public SpecialOfferCard buildSpecialOfferCard(RoomTypeDetails roomTypeDetails) {
		if(Objects.nonNull(roomTypeDetails) && Objects.nonNull(roomTypeDetails.getOccassionDetails())) {
			SpecialOfferCard specialOfferCard = new SpecialOfferCard();
			specialOfferCard.setImageUrl(roomTypeDetails.getOccassionDetails().getSpecialCardImageUrl());
			specialOfferCard.setHeaderImage(roomTypeDetails.getOccassionDetails().getHeaderImageUrl());
			specialOfferCard.setText(roomTypeDetails.getOccassionDetails().getText());
			specialOfferCard.setBgGradient(roomTypeDetails.getOccassionDetails().getBgGradient());
			specialOfferCard.setType(roomTypeDetails.getOccassionDetails().getOccassionType());
			return specialOfferCard;
		}
		return null;
	}

	public com.mmt.hotels.clientgateway.response.wrapper.LongStayBenefits buildLongStayBenefits(LongStayBenefits longStayBenefits) {
		com.mmt.hotels.clientgateway.response.wrapper.LongStayBenefits longStayBenefitsCG = null;
		if(longStayBenefits != null){
			longStayBenefitsCG = new com.mmt.hotels.clientgateway.response.wrapper.LongStayBenefits();
			longStayBenefitsCG.setTitle(longStayBenefits.getTitle());
			longStayBenefitsCG.setSubTitle(longStayBenefits.getSubTitle());
			longStayBenefitsCG.setTitleColor(longStayBenefits.getTitleColor());
			if(CollectionUtils.isNotEmpty(longStayBenefits.getInclusionsList())){
				longStayBenefits.getInclusionsList().forEach(inclusion -> {
					inclusion.setValue(inclusion.getCode());
				});
			}
			longStayBenefitsCG.setInclusionsList(longStayBenefits.getInclusionsList());
			longStayBenefitsCG.setCardId(longStayBenefits.getCardId());
			if(longStayBenefits.getBorderGradient() != null){
				BorderGradient bg = new BorderGradient();
				bg.setAngle(longStayBenefits.getBorderGradient().getAngle());
				bg.setEnd(longStayBenefits.getBorderGradient().getEnd());
				bg.setStart(longStayBenefits.getBorderGradient().getStart());
				bg.setDirection(longStayBenefits.getBorderGradient().getDirection());
				longStayBenefitsCG.setBorderGradient(bg);
			}
		}
		return longStayBenefitsCG;
	}

	public Map<String, FoodDiningRule> buildMealsMap(Map<String, MealClarity> mealsMapHES) {
		if (MapUtils.isEmpty(mealsMapHES)) {
			return null;
		}
		
		Map<String, FoodDiningRule> mealsMap = new HashMap<>();
		
		for (Map.Entry<String, MealClarity> entry : mealsMapHES.entrySet()) {
			String mealKey = entry.getKey();
			MealClarity mealDetailsHES = entry.getValue();
			
			if (mealDetailsHES != null) {
				FoodDiningRule foodDiningRule = new FoodDiningRule();
				buildMealDetails(foodDiningRule, mealDetailsHES);
				
				// Clear subtitle from meal options since this is for meals map
				if (CollectionUtils.isNotEmpty(foodDiningRule.getMealOptions())) {
					for (MealOption mealOption : foodDiningRule.getMealOptions()) {
						mealOption.setSubtitle(null);
					}
				}
				
				// Set included meals if available
				List<Meal> includedMeals = null;
				if (CollectionUtils.isNotEmpty(mealDetailsHES.getInclusive())) {
					includedMeals = buildCGMeals(mealDetailsHES.getInclusive());
					foodDiningRule.setIncludedMeals(includedMeals);
				}
				
				// Set sheet title based on whether included meals exist
				if (CollectionUtils.isNotEmpty(includedMeals)) {
					foodDiningRule.setSheetTitle(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_SHEET_TITLE_MEAL_DETAILS));
					foodDiningRule.setIncludedText(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_INCLUDED_TEXT));
					foodDiningRule.setExcludedText(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_EXCLUDED_TEXT));
					foodDiningRule.setHeading(null);
					
					// If included meals size is more than 1, remove certain fields
					if (includedMeals.size() > 1) {
						foodDiningRule.setExcludedText(null);
						foodDiningRule.setMealOptionsInfo(null);
						foodDiningRule.setMealOptions(null);
						foodDiningRule.setNote(null);
					}
				} else {
					foodDiningRule.setSheetTitle(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_SHEET_TITLE_MEALS_AVAILABLE));
				}
				
				mealsMap.put(mealKey, foodDiningRule);
			}
		}
		
		return mealsMap.isEmpty() ? null : mealsMap;
	}

	public void buildMealDetails(FoodDiningRule foodDiningRule, MealClarity mealDetailsHES) {
		if (mealDetailsHES == null || foodDiningRule == null) {
			return;
		}

		// Set heading based on meal options available
		if (CollectionUtils.isNotEmpty(mealDetailsHES.getMealOptions())) {
			if (mealDetailsHES.getMealOptions().size() > 1) {
				foodDiningRule.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_MULTI_OPTION_TITLE));
			} else {
				String optionType = mealDetailsHES.getMealOptions().get(0).getOptionType();
				if (Constants.MEAL_OPTION_TYPE_FIXED_MENU.equals(optionType)) {
					foodDiningRule.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FIXED_MENU_TITLE));
				} else if (Constants.MEAL_OPTION_TYPE_COOK.equals(optionType)) {
					foodDiningRule.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_COOK_OPTION_TITLE));
				} else {
					foodDiningRule.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_MULTI_OPTION_TITLE));
				}
			}
		} else {
			foodDiningRule.setHeading(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_MULTI_OPTION_TITLE));
		}

		// Set meal options info text for multiple options
		if (CollectionUtils.isNotEmpty(mealDetailsHES.getMealOptions()) && mealDetailsHES.getMealOptions().size() > 1) {
			foodDiningRule.setMealOptionsInfo(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_MULTI_OPTION_INFO_TEXT));
		}

		// Set note from meal details
		if (StringUtils.isNotEmpty(mealDetailsHES.getNote())) {
			foodDiningRule.setNote(mealDetailsHES.getNote());
		}

		// Build rules array
		List<DiningRuleItem> ruleItems = new ArrayList<>();

		// Add meals offered rule
		if (mealDetailsHES.getMealsOffered() != null) {
			DiningRuleItem mealsOfferedRule = new DiningRuleItem();
			mealsOfferedRule.setTitleText(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_CARD_MEALS_OFFERED));
			if (StringUtils.isNotEmpty(mealDetailsHES.getMealsOffered().getGrammar())) {
				mealsOfferedRule.setText(mealDetailsHES.getMealsOffered().getGrammar());
			}
			if (StringUtils.isNotEmpty(mealDetailsHES.getMealsOffered().getIcon())) {
				mealsOfferedRule.setIconUrl(mealDetailsHES.getMealsOffered().getIcon());
			}
			if (CollectionUtils.isNotEmpty(mealDetailsHES.getMealsOffered().getMealTypes())) {
				List<MealType> cgMealTypes = new ArrayList<>();
				for (com.mmt.hotels.model.response.staticdata.meals.MealType hesMealType : mealDetailsHES.getMealsOffered().getMealTypes()) {
					if (hesMealType != null) {
						MealType cgMealType = new MealType();
						cgMealType.setName(hesMealType.getName());
						cgMealType.setIcon(hesMealType.getIcon());
						cgMealTypes.add(cgMealType);
					}
				}
				mealsOfferedRule.setMealTypes(cgMealTypes);
			}
			ruleItems.add(mealsOfferedRule);
		}

		// Add cuisines rule
		if (mealDetailsHES.getCuisines() != null) {
			DiningRuleItem cuisinesRule = new DiningRuleItem();
			cuisinesRule.setTitleText(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_CARD_CUISINES));
			if (StringUtils.isNotEmpty(mealDetailsHES.getCuisines().getGrammar())) {
				cuisinesRule.setText(mealDetailsHES.getCuisines().getGrammar());
			}
			if (StringUtils.isNotEmpty(mealDetailsHES.getCuisines().getIcon())) {
				cuisinesRule.setIconUrl(mealDetailsHES.getCuisines().getIcon());
			}
			ruleItems.add(cuisinesRule);
		}

		// Handle meal options and charges rule
		if (CollectionUtils.isNotEmpty(mealDetailsHES.getMealOptions())) {
			// If single COOK option, extract charges into rule items
			if (mealDetailsHES.getMealOptions().size() == 1 && 
				Constants.MEAL_OPTION_TYPE_COOK.equals(mealDetailsHES.getMealOptions().get(0).getOptionType())) {

				com.mmt.hotels.model.response.staticdata.meals.MealOption cookOptionHES = mealDetailsHES.getMealOptions().get(0);

				if (CollectionUtils.isNotEmpty(cookOptionHES.getMeals())) {
					com.mmt.hotels.model.response.staticdata.meals.Meal cookItem = cookOptionHES.getMeals().get(0);
					if (CollectionUtils.isNotEmpty(cookItem.getDetails())) {
						for (com.mmt.hotels.model.response.staticdata.meals.MealDetail detail : cookItem.getDetails()) {
							if (MEAL_CONTEXT_PRICING.equalsIgnoreCase(detail.getContext())) {
								DiningRuleItem chargesRule = new DiningRuleItem();
								chargesRule.setTitleText(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_CARD_CHARGES));
								chargesRule.setText(detail.getValue());
								chargesRule.setIconUrl(detail.getIcon());
								chargesRule.setNote(detail.getNote());
								ruleItems.add(chargesRule);
							}
						}
					}
				}

			} else {
				// For all other cases, set meal options directly
				foodDiningRule.setMealOptions(buildCGMealOptions(mealDetailsHES.getMealOptions()));
			}
		}

		foodDiningRule.setRules(CollectionUtils.isNotEmpty(ruleItems) ? ruleItems : null);
	}

	private List<MealOption> buildCGMealOptions(List<com.mmt.hotels.model.response.staticdata.meals.MealOption> mealOptionsHES) {
		if (CollectionUtils.isEmpty(mealOptionsHES)) {
			return null;
		}

		List<MealOption> mealOptionsCG = new ArrayList<>();
		boolean hasMultipleOptions = mealOptionsHES.size() > 1;
		
		for (com.mmt.hotels.model.response.staticdata.meals.MealOption mealOptionHES : mealOptionsHES) {
			if (mealOptionHES != null) {
				MealOption mealOptionCG = new MealOption();
				
				// Only set optionLabel and title if there are multiple options
				if (hasMultipleOptions) {
					mealOptionCG.setOptionLabel(mealOptionHES.getOptionLabel());
					mealOptionCG.setTitle(mealOptionHES.getTitle());
				}
				
				mealOptionCG.setOptionType(mealOptionHES.getOptionType());
				mealOptionCG.setSubtitle(mealOptionHES.getSubTitle());

				if (CollectionUtils.isNotEmpty(mealOptionHES.getMeals())) {
					mealOptionCG.setMeals(buildCGMeals(mealOptionHES.getMeals()));
				}
				
				mealOptionsCG.add(mealOptionCG);
			}
		}
		
		return mealOptionsCG;
	}

	private List<Meal> buildCGMeals(List<com.mmt.hotels.model.response.staticdata.meals.Meal> mealsHES) {
		if (CollectionUtils.isEmpty(mealsHES)) {
			return null;
		}
		List<Meal> mealsCG = new ArrayList<>();
		
		for (com.mmt.hotels.model.response.staticdata.meals.Meal mealHES : mealsHES) {
			if (mealHES != null) {
				Meal mealCG = new Meal();
				mealCG.setType(mealHES.getType());
				mealCG.setDisplayName(mealHES.getDisplayName());
				mealCG.setSubtext(mealHES.getSubText());
				mealCG.setNote(mealHES.getNote());
				if (CollectionUtils.isNotEmpty(mealHES.getDetails())) {
					mealCG.setDetails(buildCGMealDetails(mealHES.getDetails()));
				}
				mealsCG.add(mealCG);
			}
		}
		return mealsCG;
	}

	private List<MealDetail> buildCGMealDetails(List<com.mmt.hotels.model.response.staticdata.meals.MealDetail> mealDetailsHES) {
		if (CollectionUtils.isEmpty(mealDetailsHES)) {
			return null;
		}
		List<MealDetail> mealDetailsCG = new ArrayList<>();
		for (com.mmt.hotels.model.response.staticdata.meals.MealDetail mealDetailHES : mealDetailsHES) {
			if (mealDetailHES != null) {
				MealDetail mealDetailCG = new MealDetail();
				mealDetailCG.setValue(mealDetailHES.getValue());
				mealDetailCG.setIcon(mealDetailHES.getIcon());
				mealDetailCG.setNote(mealDetailHES.getNote());

				mealDetailsCG.add(mealDetailCG);
			}
		}
		return mealDetailsCG;
	}

	private String updateTierName(com.mmt.hotels.model.response.mmtprime.BlackInfo bInfo) {
		return !bInfo.isBlackRevamp() && blackV1TierMap.containsKey(bInfo.getTierName()) ?
				blackV1TierMap.get(bInfo.getTierName()) : bInfo.getTierName();
	}

	public AdditionalMandatoryCharges buildAdditionalCharges(AdditionalChargesBO additionalChargesBO,
															 boolean showTransfersFeeTxt, String listingType,
															 String pageContext, Map<String, String> expDataMap) {
		if (CollectionUtils.isEmpty(additionalChargesBO.getAdditionalFees()))
			return null;
			
		boolean isIhHouseRuleUiV2Enabled = StringUtils.isNotEmpty(additionalChargesBO.getCountryCode()) 
		        && !Constants.DH_COUNTRY_CODE.equalsIgnoreCase(additionalChargesBO.getCountryCode()) 
		        && utility.isExperimentTrue(expDataMap, ExperimentKeys.IH_HOUSE_RULE_UI_REVAMP.getKey())
		        && (PAGE_CONTEXT_REVIEW.equalsIgnoreCase(pageContext) || PAGE_CONTEXT_DETAIL.equalsIgnoreCase(pageContext));
			
		AdditionalMandatoryCharges additionalMandatoryCharges = new AdditionalMandatoryCharges();
		additionalMandatoryCharges.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.MANDATORY_CHARGES_SECTION_TITLE));
		
		String subtitle;
		if(DH_HOTEL_CURRENCY.equalsIgnoreCase(additionalChargesBO.getHotelierCurrency())) {
			subtitle = MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.MANDATORY_CHARGES_SECTION_DESC_DH),
					StringUtils.isNotEmpty(additionalChargesBO.getPropertyType()) ? additionalChargesBO.getPropertyType().toLowerCase() : EMPTY_STRING);
		} else {
			subtitle = MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.MANDATORY_CHARGES_SECTION_DESC),
					StringUtils.isNotEmpty(additionalChargesBO.getPropertyType()) ? additionalChargesBO.getPropertyType().toLowerCase() : EMPTY_STRING);
		}

		String description = "";
		if (additionalChargesBO.isRecommendationFlow()) {
			description = polyglotService.getTranslatedData(
					ConstantsTranslation.MANDATORY_CHARGES_ROOM_DESC_RECOMMENDATION
			);
		} else if (StringUtils.isNotBlank(additionalChargesBO.getRoomName())) {
			description = polyglotService.getTranslatedData(ConstantsTranslation.MANDATORY_CHARGES_ROOM_DESC)
					.replace("{ROOM_NAME}", additionalChargesBO.getRoomName());
		}

		if (isIhHouseRuleUiV2Enabled) {
			// Append subtitle to description
			if (StringUtils.isNotBlank(description)) {
				additionalMandatoryCharges.setDescription(description + ". " + subtitle);
			} else {
				additionalMandatoryCharges.setDescription(subtitle);
			}

			if (StringUtils.isNotEmpty(pageContext) && PAGE_CONTEXT_DETAIL.equalsIgnoreCase(pageContext)) {
				// Set new subtitle
				additionalMandatoryCharges.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.IMPORTANT_INFORMATION_SUBTITLE_IH));
			}
		} else {
			additionalMandatoryCharges.setSubTitle(subtitle);
			if (StringUtils.isNotBlank(description)) {
				additionalMandatoryCharges.setDescription(description);
			}
		}
		
		// Remove description if bnb pokus is off.
		if (!utility.isExperimentTrue(expDataMap, EXTRA_BNB_EXP_KEY)) {
			additionalMandatoryCharges.setDescription(null);
		}
		additionalMandatoryCharges.setShowWithPropertyRules(utility.isExperimentTrue(expDataMap, SHOW_RULES_EXP_KEY));
		double sum = additionalChargesBO.getAdditionalFees().stream().mapToDouble(AdditionalFees::getAmount).sum();
		additionalMandatoryCharges.setAmount(Utility.round(sum, 0));
		additionalMandatoryCharges.setCurrency(additionalChargesBO.getUserCurrency());
		if (StringUtils.isNotEmpty(pageContext) && PAGE_CONTEXT_DETAIL.equalsIgnoreCase(pageContext)) {
			additionalMandatoryCharges.setShowReadAndAgree(false);
			additionalMandatoryCharges.setDisclaimer(polyglotService.getTranslatedData(ConstantsTranslation.MANDATORY_CHARGES_DETAIL_PAGE_DISCLAIMER));
		} else {
			additionalMandatoryCharges.setShowReadAndAgree(checkIfMandatoryChargesTooHigh(additionalMandatoryCharges.getAmount(), additionalChargesBO.getBookingAmount()));
		}

		additionalMandatoryCharges.setBreakup(new ArrayList<>());
		for (AdditionalFees additionalFees : additionalChargesBO.getAdditionalFees()) {
			AdditionalMandatoryChargesBreakup breakup = new AdditionalMandatoryChargesBreakup();
			breakup.setTitle(buildMandatoryChargesTitleText(additionalFees));
			String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
			if (!Utility.isRegionGccOrKsa(region)) {
				if (StringUtils.isNotBlank(additionalFees.getName())) {
					breakup.setDescription(additionalFees.getName() + ". " + additionalFees.getDescription());
				} else {
					breakup.setDescription(additionalFees.getDescription());
				}
			}
			breakup.setInfoText(buildMandatoryChargesInfoText(additionalFees));
			breakup.setAmount(Utility.round(additionalFees.getAmount(), 0));
			breakup.setCurrency(additionalFees.getCurrency());
			if (StringUtils.isNotBlank(additionalFees.getCurrency()) && !additionalFees.getCurrency().equalsIgnoreCase(additionalChargesBO.getHotelierCurrency())) {
				breakup.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.MANDATORY_CHARGES_BREAKUP_SUBTITLE));

				if (!Utility.isRegionGccOrKsa(region)) {
					breakup.setHotelierCurrency(additionalChargesBO.getHotelierCurrency());
					breakup.setHotelierCurrencyAmount(Utility.round(additionalFees.getAmount() * additionalChargesBO.getConversionFactor(), 2));
				}
			}
			//Added Maldives transfer charges message
			if (showTransfersFeeTxt && TRANSFERS.equalsIgnoreCase(additionalFees.getLeafCategory())) {
				if (CITY_CODE_MALDIVES.equalsIgnoreCase(additionalChargesBO.getCityCode())) {
					breakup.setChargesMsg(polyglotService.getTranslatedData(ADDITIONAL_FEE_TRANSFERS_MSG));
					breakup.setChargesMsgV2(polyglotService.getTranslatedData(ADDITIONAL_FEE_TRANSFERS_HTML) + polyglotService.getTranslatedData(ADDITIONAL_FEE_TRANSFERS_MSG));
				}
			}
			Triple<String, String, List<String>> mandatoryChargesAmountSubText = buildMandatoryChargesAmountSubText(additionalFees,listingType,additionalChargesBO.getPropertyType());
			breakup.setInclusions(buildInclusionListForMandatoryCharges(additionalFees, mandatoryChargesAmountSubText));
			if (mandatoryChargesAmountSubText != null) {
				String adultSubText = mandatoryChargesAmountSubText.getLeft();
				String childSubText = mandatoryChargesAmountSubText.getMiddle();
				if (StringUtils.isNotEmpty(adultSubText)) {
					if (StringUtils.isNotEmpty(childSubText)) {
						breakup.setAmountSubText(adultSubText + ADDITIONAL_FEE_SUBTEXT_LINE_SEPARATOR + childSubText);
					} else {
						breakup.setAmountSubText(adultSubText);
					}
				}
			}
			if (utility.isExperimentTrue(expDataMap, EXTRA_BNB_EXP_KEY)) {
				breakup.setCategoryDesc(buildCategoryDescText(additionalFees, additionalChargesBO.getCityName()));
				breakup.setAmountDesc(buildAdditionalFeeAmountDesc(additionalFees));
			}
			additionalMandatoryCharges.getBreakup().add(breakup);
		}
		return additionalMandatoryCharges;
	}

	private String buildAdditionalFeeAmountDesc(AdditionalFees additionalFees) {
		if (additionalFees.getApplicableDaysCount() > 0) {
			return polyglotService.getTranslatedData(ConstantsTranslation.MANDATORY_CHARGES_AMOUNT_DESC)
					.replace("{NO_OF_NIGHTS}", String.valueOf(additionalFees.getApplicableDaysCount()));
		}
		return null;
	}

	private String buildCategoryDescText(AdditionalFees additionalFees, String cityName) {
		String additionalFeeType = getMandatoryChargesPropertyType(additionalFees);
		String additionalFeeSubType = getMandatoryChargesPropertySubType(additionalFees);

		// Transfers short description.
		if (TRANSFERS.equalsIgnoreCase(additionalFees.getLeafCategory())) {
			if (StringUtils.isNotBlank(additionalFeeSubType)) {
				String configKey = additionalFeeSubType.replace(SPACE, Constants.UNDERSCORE)
						.replace("&", EMPTY_STRING);
				return MapUtils.isNotEmpty(mandatoryChargesTransfersShortDescMap)
						? mandatoryChargesTransfersShortDescMap.get(configKey)
						: null;
			}
			return null;
		}
		// Gala meals short description.
		if (GALA_MEALS.equalsIgnoreCase(additionalFees.getLeafCategory())) {
			return StringUtils.isNotBlank(additionalFeeSubType)
					? polyglotService.getTranslatedData(GALA_MEALS_SHORT_DESC).replace("{GALA_MEAL}", additionalFeeSubType.toLowerCase())
					: null;
		}

		String slugifiedType = additionalFeeType.replace(SPACE, Constants.UNDERSCORE).replaceAll("[+/]", EMPTY_STRING);
		// Green Tax short description.
		if (GREEN_TAX.equalsIgnoreCase(slugifiedType)) {
			return polyglotService.getTranslatedData(GREEN_TAX_SHORT_DESC);
		}
		String additionalFeeLeafCategory = StringUtils.isNotBlank(additionalFees.getLeafCategory())
				? additionalFees.getLeafCategory() : EMPTY_STRING;
		// City Tax short description.
		if (CollectionUtils.isNotEmpty(commonConfigConsul.getMandatoryChargesCityTaxCategories())
				&& commonConfigConsul.getMandatoryChargesCityTaxCategories().contains(additionalFeeLeafCategory)) {
			return polyglotService.getTranslatedData(MANDATORY_CHARGES_CITY_TAX_SHORT_DESC)
					.replace("{TAX_TYPE}", StringUtils.isNotEmpty(additionalFeeType) ? additionalFeeType : additionalFeeLeafCategory)
					.replace("{CITY_NAME}", cityName);
		}
		// Service Fee short description.
		if (CollectionUtils.isNotEmpty(commonConfigConsul.getMandatoryChargesServiceCategories())
				&& commonConfigConsul.getMandatoryChargesServiceCategories().contains(additionalFeeLeafCategory)) {
			return polyglotService.getTranslatedData(MANDATORY_CHARGES_SERVICE_FEE_SHORT_DESC);
		}
		return null;
	}

	private String buildMandatoryChargesTitleText(AdditionalFees additionalFees) {
		if (StringUtils.isNotEmpty(additionalFees.getLeafCategory()) && CollectionUtils.isNotEmpty(additionalFees.getPropertyType())) {
			StringBuilder stringBuilder = new StringBuilder();
			String type = getMandatoryChargesPropertyType(additionalFees);
			String subType = getMandatoryChargesPropertySubType(additionalFees);
			if (TRANSFERS.equalsIgnoreCase(additionalFees.getLeafCategory())) {
				if (StringUtils.isNotEmpty(type)) {
					stringBuilder.append(getMandatoryChargesPropertyType(additionalFees)).append(SPACE);
				}
				stringBuilder.append("Transfer");
			} else if (compulsoryChargesTaxTypes.contains(additionalFees.getLeafCategory())) {
				stringBuilder.append(StringUtils.isNotEmpty(type) ? type : additionalFees.getLeafCategory());
			} else if (GALA_MEALS.equalsIgnoreCase(additionalFees.getLeafCategory())) {
				stringBuilder.append("Gala").append(SPACE);
				if (StringUtils.isNotEmpty(subType)) {
					stringBuilder.append(subType);
				} else {
					stringBuilder.append("Meals");
				}
			}

			return stringBuilder.toString();
		}
		return additionalFees.getLeafCategory();
	}

	private boolean checkIfChargesMandatoryOrNot(AdditionalFees additionalFees) {
		return additionalFees.getMandatory() != null && additionalFees.getMandatory();
	}

	private String getMandatoryChargesPropertyType(AdditionalFees additionalFees) {
		if (CollectionUtils.isNotEmpty(additionalFees.getPropertyType())) {
			return additionalFees.getPropertyType().get(0);
		}
		return EMPTY_STRING;
	}

	private String getMandatoryChargesPropertySubType(AdditionalFees additionalFees) {
		if (CollectionUtils.isNotEmpty(additionalFees.getPropertySubType())) {
			return additionalFees.getPropertySubType().get(0);
		}
		return EMPTY_STRING;
	}

	private List<String> buildInclusionListForMandatoryCharges(AdditionalFees additionalFees, Triple<String, String, List<String>> mandatoryChargesAmountSubText) {
		if (StringUtils.isNotEmpty(additionalFees.getLeafCategory())) {
			List<String> inclusionList = new ArrayList<>();
			if (TRANSFERS.equalsIgnoreCase(additionalFees.getLeafCategory())) {
				inclusionList.add("Transfer is " + (checkIfChargesMandatoryOrNot(additionalFees) ? "mandatory" : "non-mandatory"));

				if (CollectionUtils.isNotEmpty(additionalFees.getPropertyType())) {
					StringBuilder transferType = new StringBuilder();
					transferType.append("Type of Transfer - ").append(getMandatoryChargesPropertyType(additionalFees));
					if (StringUtils.isNotEmpty(getMandatoryChargesPropertySubType(additionalFees))) {
						transferType.append(" - ").append(getMandatoryChargesPropertySubType(additionalFees));
					}
					inclusionList.add(transferType.toString());
				}
			}

			if (additionalFees.getPrice() != null && (additionalFees.getPrice().getDefaultPrice() == null || additionalFees.getPrice().getDefaultPrice() == 0.0)) {
				buildPriceInclusionForMandatoryCharges("adult", additionalFees.getTotalAdults(), mandatoryChargesAmountSubText.getLeft(), mandatoryChargesAmountSubText.getRight(), inclusionList);
				if (additionalFees.getTotalChild() > 0) buildPriceInclusionForMandatoryCharges("child", additionalFees.getTotalChild(), mandatoryChargesAmountSubText.getMiddle(), mandatoryChargesAmountSubText.getRight(), inclusionList);
			}

			if (GALA_MEALS.equalsIgnoreCase(additionalFees.getLeafCategory()) && CollectionUtils.isEmpty(inclusionList)
					&& CollectionUtils.isNotEmpty(additionalFees.getPropertySubType())) {
				inclusionList.add("Complimentary free " + getMandatoryChargesPropertySubType(additionalFees) + " for all");
				if (additionalFees.getTotalChild() > 0 && StringUtils.isEmpty(mandatoryChargesAmountSubText.getMiddle())) {
					inclusionList.add("Complimentary free " + getMandatoryChargesPropertySubType(additionalFees) + " for all children");
				}
			}

			return inclusionList;
		}

		return null;
	}

	private void buildPriceInclusionForMandatoryCharges(String type, int count, String feeText, List<String> typeOfCharges, List<String> inclusionList) {
		List<String> fees = Arrays.asList(feeText.split(ADDITIONAL_FEE_SUBTEXT_SEPARATOR));
		if (CollectionUtils.isNotEmpty(fees) && fees.size() > 1) {
			String formattedType = formatType(type, count);
			if (count > 1) {
				String typeOfCharge = type.equalsIgnoreCase("child") && typeOfCharges.size() > 2 ? typeOfCharges.get(2) : typeOfCharges.get(1);
				inclusionList.add(new StringBuilder("Price ").append(typeOfCharges.get(0)).append(" ").append(typeOfCharge).append(" - ").append(fees.get(fees.size() - 1)).toString());
			}
			inclusionList.add(new StringBuilder("Total price for ").append(count).append(" ").append(formattedType).append(" - ").append(feeText).toString());
		}
	}

	private String formatType(String type, int count) {
		switch (type.toLowerCase()) {
			case "adult":
				return count > 1 ? type + "s" : type;
			case "child":
				return count > 1 ? type + "ren" : type;
		}
		return type;
	}

	private String buildMandatoryChargesInfoText(AdditionalFees additionalFees) {
		if (MapUtils.isNotEmpty(mandatoryChargesDisclaimerMap) && CollectionUtils.isNotEmpty(additionalFees.getPropertyType())) {
			String propertyType = additionalFees.getPropertyType().get(0).replace(SPACE, Constants.UNDERSCORE).replaceAll("[+/]", EMPTY_STRING);
			String subPropertyType = CollectionUtils.isEmpty(additionalFees.getPropertySubType()) ? Constants.ALL : additionalFees.getPropertySubType().get(0).replace(SPACE, Constants.UNDERSCORE).replace("&", EMPTY_STRING);
			String configKey = String.join(Constants.DOUBLE_UNDERSCORE, "Type", propertyType, "Subtype", subPropertyType);
			return mandatoryChargesDisclaimerMap.get(configKey);
		}
		return null;
	}

	private boolean checkIfMandatoryChargesTooHigh(double mandatoryChargesTotalAmount, double bookingAmount) {
		return mandatoryChargesTotalAmount > (bookingAmount / 2);
	}

	private Triple<String, String, List<String>> buildMandatoryChargesAmountSubText(AdditionalFees additionalFees, String listingType, String propertyType) {
		if (null == additionalFees.getPrice()) {
			return null;
		}
		StringBuilder adultSubText = new StringBuilder();
		StringBuilder childSubText = new StringBuilder();
		List<String> typeOfCharges = new ArrayList<>();
		AdditionalFeesPrice additionalFeesPrice = additionalFees.getPrice();
		if (additionalFeesPrice.getDefaultPrice() != null && additionalFeesPrice.getDefaultPrice() > 0.0) {
			adultSubText.append(polyglotService.getTranslatedData(ConstantsTranslation.MANDATORY_CHARGES_DEFAULT_PRICE_TEXT));
		} else if (null != additionalFeesPrice.getPerStayRoom() && additionalFeesPrice.getPerStayRoom() > 0.0 && additionalFees.getTotalRooms() > 0) {
			adultSubText.append(additionalFees.getTotalRooms())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getRoomsSubText(additionalFees.getTotalRooms(),listingType,propertyType))
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
					.append(additionalFees.getCurrency())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getRoundedAmount(additionalFeesPrice.getPerStayRoom()));
			typeOfCharges.add(Constants.ADDITIONAL_FEE_PER_STAY_CHARGE);
			typeOfCharges.add(Constants.ADDITIONAL_FEE_PER_ROOM_CHARGE);
		} else if (null != additionalFeesPrice.getPerStayAdult() && additionalFeesPrice.getPerStayAdult() > 0.0 && additionalFees.getTotalAdults() > 0) {
			adultSubText.append(additionalFees.getTotalAdults())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getAdultsSubText(additionalFees.getTotalAdults()))
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
					.append(additionalFees.getCurrency())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getRoundedAmount(additionalFeesPrice.getPerStayAdult()));
			typeOfCharges.add(Constants.ADDITIONAL_FEE_PER_STAY_CHARGE);
			typeOfCharges.add(Constants.ADDITIONAL_FEE_PER_ADULT_CHARGE);
			if (null != additionalFeesPrice.getPerStayChild() && additionalFeesPrice.getPerStayChild() > 0.0 && additionalFees.getTotalChild() > 0) {
				childSubText.append(additionalFees.getTotalChild())
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
						.append(getChildSubText(additionalFees.getTotalChild()))
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
						.append(additionalFees.getCurrency())
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
						.append(getRoundedAmount(additionalFeesPrice.getPerStayChild()));
				typeOfCharges.add(Constants.ADDITIONAL_FEE_PER_CHILD_CHARGE);
			}
		} else if (null != additionalFeesPrice.getPerNightRoom() && additionalFeesPrice.getPerNightRoom() > 0.0 && additionalFees.getTotalRooms() > 0) {
			adultSubText.append(additionalFees.getApplicableDaysCount())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getNightsSubText(additionalFees.getApplicableDaysCount()))
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
					.append(additionalFees.getTotalRooms())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getRoomsSubText(additionalFees.getTotalRooms(),listingType,propertyType))
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
					.append(additionalFees.getCurrency())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getRoundedAmount(additionalFeesPrice.getPerNightRoom()));
			typeOfCharges.add(Constants.ADDITIONAL_FEE_PER_NIGHT_CHARGE);
			typeOfCharges.add(Constants.ADDITIONAL_FEE_PER_ROOM_CHARGE);
		} else if (null != additionalFeesPrice.getPerNightAdult() && additionalFeesPrice.getPerNightAdult() > 0.0 && additionalFees.getTotalAdults() > 0) {
			adultSubText.append(additionalFees.getApplicableDaysCount())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getNightsSubText(additionalFees.getApplicableDaysCount()))
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
					.append(additionalFees.getTotalAdults())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getAdultsSubText(additionalFees.getTotalAdults()))
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
					.append(additionalFees.getCurrency())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getRoundedAmount(additionalFeesPrice.getPerNightAdult()));
			typeOfCharges.add(Constants.ADDITIONAL_FEE_PER_NIGHT_CHARGE);
			typeOfCharges.add(Constants.ADDITIONAL_FEE_PER_ADULT_CHARGE);
			if (null != additionalFeesPrice.getPerNightChild() && additionalFeesPrice.getPerNightChild() > 0.0 && additionalFees.getTotalChild() > 0) {
				childSubText.append(additionalFees.getApplicableDaysCount())
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
						.append(getNightsSubText(additionalFees.getApplicableDaysCount()))
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
						.append(additionalFees.getTotalChild())
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
						.append(getChildSubText(additionalFees.getTotalChild()))
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
						.append(additionalFees.getCurrency())
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
						.append(getRoundedAmount(additionalFeesPrice.getPerNightChild()));
				typeOfCharges.add(Constants.ADDITIONAL_FEE_PER_CHILD_CHARGE);
			}
		}

		return new ImmutableTriple<>(adultSubText.toString(), childSubText.toString(), typeOfCharges);
	}


	public int getRoundedAmount(Double amount) {
		return Utility.round(amount, 0).intValue();
	}

	private String getChildSubText(int totalChild) {
		if (totalChild > 1)
			return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_CHILDREN);
		return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_CHILD);
	}

	private String getAdultsSubText(int totalAdults) {
		if (totalAdults > 1)
			return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_ADULTS);
		return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_ADULT);
	}

	public String getRoomsSubText(int totalRooms, String listingType, String propertyType) {
		if (LISTING_TYPE_ENTIRE.equalsIgnoreCase(listingType) && StringUtils.isNotBlank(propertyType)) {
			return StringUtils.capitalize(listingType) + SPACE + propertyType + (totalRooms > 1 ? 's' : "");
		} else {
			if (totalRooms > 1)
				return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_ROOMS);
			return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_ROOM);
		}
	}

	private String getNightsSubText(int applicableDays) {
		if (applicableDays > 1)
			return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_NIGHTS);
		return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_NIGHT);
	}

	public CorpApprovalInfo buildCorpApprovalInfo(com.mmt.hotels.model.response.corporate.CorpMetaInfo corpMetaData, boolean tcsV2FlowEnabled) {
		if (corpMetaData == null) {
			return null;
		}
		CorpApprovalInfo corpMetaInfo = new CorpApprovalInfo();
		corpMetaInfo.setApprovalRequired(corpMetaData.isApprovalRequired());
		if (corpMetaData.getValidationPayload() != null) {
			corpMetaInfo.setBlockOopBooking(corpMetaData.getValidationPayload().getBlockOopBooking());
			corpMetaInfo.setBlockSkipApproval(corpMetaData.getValidationPayload().getBlockSkipApproval());
			corpMetaInfo.setWithinPolicy(corpMetaData.getValidationPayload().isWithinPolicy());
			corpMetaInfo.setFailureReasons(corpMetaData.getValidationPayload().getFailureReasons());
			corpMetaInfo.setApprovalType(corpMetaData.getValidationPayload().getApprovalType());
		}
		if (corpMetaData.getQuickCheckout() != null) {
			// sending quickCheckout flag from corp to client
			corpMetaInfo.setWalletQuickPayAllowed(corpMetaData.getQuickCheckout() && !tcsV2FlowEnabled);
		}
		return corpMetaInfo;
	}

	public List<ApprovingManager> buildManagers(List<com.mmt.hotels.model.response.corporate.ApprovingManager> approvingManagers) {

		if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(approvingManagers)) {
			List<ApprovingManager> managerList = new ArrayList<>();

			for (com.mmt.hotels.model.response.corporate.ApprovingManager managerHES : approvingManagers) {
				ApprovingManager managerCG = new ApprovingManager();
				managerCG.setId(managerHES.getId());
				managerCG.setName(managerHES.getName());
				managerCG.setBusinessEmailId(managerHES.getBusinessEmailId());
				managerCG.setManagerType(managerHES.getManagerType());
				managerCG.setPhoneNumber(managerHES.getPhoneNumber());
				managerList.add(managerCG);
			}

			return managerList;
		}
		return null;
	}

	public List<CorpRateTags> buildTags(List<CorpTags> tags) {

		if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(tags)) {
			List<CorpRateTags> tagsCG = new ArrayList<>();

			for (CorpTags tagCB : tags) {
				CorpRateTags tagCG = new CorpRateTags();
				tagCG.setTagDescription(tagCB.getTagDescription());
				tagCG.setTagName(tagCB.getTagName());

				tagsCG.add(tagCG);
			}
			return tagsCG;
		}

		return null;
	}

	// HTL-42803:  TO-DO remove boolean isBnplOneVariant node once BNPLVariant Enum changes are completely live.
	public List<RatePlan> buildRateplanList(List<RoomType> roomTypes, BNPLDetails bnplDetails, boolean isBnplOneVariant, BNPLVariant bnplVariant, Boolean isPeakDate, boolean enableThemification) {
		List<RatePlan> ratePlanList = new ArrayList<>();
		boolean bnplApplicable = false;
		if (bnplDetails != null)
			bnplApplicable = bnplDetails.isBnplApplicable();
		if (CollectionUtils.isNotEmpty(roomTypes)) {
			String confirmationPolicyType = null;
			RatePolicy confirmationPolicy = getConfirmationPolicy(roomTypes);
			if (confirmationPolicy != null) {
				confirmationPolicyType = confirmationPolicy.getValue();
			}
			for (RoomType roomType : roomTypes) {
				String roomCode = roomType.getRoomTypeCode();
				if (MapUtils.isNotEmpty(roomType.getRatePlanList())) {
					for (Map.Entry<String, com.mmt.hotels.model.response.pricing.RatePlan> entry : roomType.getRatePlanList().entrySet()) {
						String ratePlanCode = entry.getKey();
						String policyDateTime = null;
						com.mmt.hotels.model.response.pricing.RatePlan ratePlanHES = entry.getValue();

						RatePlan rtPlan = new RatePlan();
						rtPlan.setRoomCode(roomCode);
						rtPlan.setCode(ratePlanCode);
						rtPlan.setCancellationTimeline(buildCancellationTimeline(ratePlanHES.getCancellationTimeline(), bnplVariant));
						rtPlan.setCancellationPolicyTimeline(buildCancellationPolicyTimeline(ratePlanHES.getCancellationTimeline(), enableThemification, bnplVariant));
						if (ratePlanHES.getCancellationTimeline() != null) {
							policyDateTime = utility.concatenateDateAndTime(ratePlanHES.getCancellationTimeline().getCardChargeDate(), ratePlanHES.getCancellationTimeline().getCardChargeDateTime());
						}
						String partialRefundText = utility.buildPartialRefundDateText(ratePlanHES.getCancellationTimeline());
						rtPlan.setCancellationPolicy(utility.transformCancellationPolicy(ratePlanHES.getCancelPenaltyList(), bnplApplicable, isBnplOneVariant, bnplVariant, confirmationPolicyType, polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT), null, Optional.ofNullable(ratePlanHES.getMpFareHoldStatus()), partialRefundText, false));
						utility.updateCancellationPolicyText(utility.isFreeCancellation(ratePlanHES.getCancelPenaltyList()) && enableThemification, rtPlan);
						updateCancellationPolicyFCSubtext(rtPlan.getCancellationPolicy(), isPeakDate, policyDateTime, enableThemification,ratePlanHES.getCancellationTimeline()!=null?ratePlanHES.getCancellationTimeline().getPricerBnplMessage():null);
						updateCancelPolicyDescription(rtPlan, ratePlanHES.getCancelPenaltyList());
						ratePlanList.add(rtPlan);
					}
				}
			}
		}
		return ratePlanList;

	}

	/*
	 *  This method will return amenity displayName(facility Name,text in bracket)
	 *  Case 1: empty displayType -> facility name
	 *  Case 2: displayType -> 2 -> L0(L1) – L2',L2'',L2''
	 *  Case 3: displayType -> 1 -> L0(L1,L1',L1''')
	 *
	 */
	public List<SelectRoomAmenities> getAmenities(List<FacilityGroup> amenities, boolean isAmenitiesV2Enabled) {
		List<SelectRoomAmenities> amenitiesList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(amenities)) {
			for (FacilityGroup fg : amenities) {
				SelectRoomAmenities selectRoomAmenities = new SelectRoomAmenities();
				selectRoomAmenities.setId(fg.getId());
				selectRoomAmenities.setName(fg.getName());
				selectRoomAmenities.setPillTitle(fg.getName());

				if (StringUtils.isNotEmpty(fg.getAccessType())) {
					AmenitiesTag tag = new AmenitiesTag();
					tag.setId(fg.getAccessType());
					tag.setText(fg.getAccessType());
					tag.setColor(utility.getColorBasedOnTag(fg.getAccessType()));
					selectRoomAmenities.setAmenitySectionTag(tag);
				}

				if(fg.isHideOnDesktop()){
					selectRoomAmenities.setHideOnDesktop(fg.isHideOnDesktop());
				}
				if (CollectionUtils.isNotEmpty(fg.getFacilities())) {
					selectRoomAmenities.setFacilities(processFacilities(fg.getFacilities(), false));
				}
				if (isAmenitiesV2Enabled && CollectionUtils.isNotEmpty(selectRoomAmenities.getFacilities()) && CollectionUtils.isNotEmpty(fg.getStrikeoutFacilities())) {
					selectRoomAmenities.getFacilities().addAll(processFacilities(fg.getStrikeoutFacilities(), true));
				}
				selectRoomAmenities.setImages(fg.getImages());
				amenitiesList.add(selectRoomAmenities);
			}
		}
		return amenitiesList;
	}

	/**
	 * Process a list of facilities and convert them into SelectRoomFacility objects
	 * @param facilities List of facilities to process
	 * @return List of processed SelectRoomFacility objects
	 */
	private List<SelectRoomFacility> processFacilities(List<Facility> facilities, boolean isStrikeThrough) {
		List<SelectRoomFacility> selectRoomFacilities = new ArrayList<>();
		if (CollectionUtils.isEmpty(facilities)) {
			return selectRoomFacilities;
		}
		for (Facility facility : facilities) {
			SelectRoomFacility selectRoomFacility = new SelectRoomFacility();
			selectRoomFacility.setName(facility.getName());
			selectRoomFacility.setIconUrl(facility.getIconUrl());
			selectRoomFacility.setStrikeThrough(isStrikeThrough);
			if (CollectionUtils.isNotEmpty(facility.getChildAttributes()) && StringUtils.isNotEmpty(facility.getDisplayType())) {
				if ("1".equalsIgnoreCase(facility.getDisplayType())) {
					StringBuilder stringBuilder = new StringBuilder();
					for (AttributesFacility cf : facility.getChildAttributes()) {
						stringBuilder.append(cf.getName()).append(Constants.COMMA);
					}
					selectRoomFacility.setSubText(Constants.AMENITIES_OPEN_BRACE +
							StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE);
				}
				if ("2".equalsIgnoreCase(facility.getDisplayType())) {
					StringBuilder stringBuilder = new StringBuilder();
					AttributesFacility childAttribute = facility.getChildAttributes().get(0);
					stringBuilder.append(childAttribute.getName())
							.append(Constants.SPACE)
							.append(Constants.HYPEN)
							.append(Constants.SPACE);
					if (CollectionUtils.isNotEmpty(childAttribute.getSubAttributes())) {
						for (SubAttributeFacility subAttributeFacility : childAttribute.getSubAttributes()) {
							stringBuilder.append(subAttributeFacility.getName()).append(Constants.COMMA);
						}
					}
					selectRoomFacility.setSubText(Constants.AMENITIES_OPEN_BRACE +
							StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE);
				}
			}
			selectRoomFacilities.add(selectRoomFacility);
		}
		return selectRoomFacilities;
	}

	public List<String> getHighlightedAmenities(List<FacilityGroup> highlightedAmenities) {
		List<String> hltAmnties = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(highlightedAmenities)) {
			for (FacilityGroup fg : highlightedAmenities) {
				if (CollectionUtils.isNotEmpty(fg.getFacilities())) {
					for (Facility facility : fg.getFacilities()) {
						hltAmnties.add(facility.getName());
					}
				}
			}
		}
		return hltAmnties;
	}

	public List<HighlightedAmenity> getHighlightedAmenitiesV2(List<FacilityGroup> highlightedAmenities) {
		List<HighlightedAmenity> hltAmnties = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(highlightedAmenities)) {
			for (FacilityGroup fg : highlightedAmenities) {
				if (CollectionUtils.isNotEmpty(fg.getFacilities())) {
					for (Facility facility : fg.getFacilities()) {
						HighlightedAmenity amenity = new HighlightedAmenity();
						amenity.setTitle(facility.getName());
						amenity.setIconUrl(facility.getIconUrl());
						hltAmnties.add(amenity);
					}
				}
			}
		}
		return hltAmnties;
	}

	public com.mmt.hotels.clientgateway.response.moblanding.ListPersonalizationResponse buildListPersonalizationResponse(com.mmt.hotels.pojo.listing.personalization.ListPersonalizationResponse listPersonalizationResponse, String client, LinkedHashMap<String,String> expDataMap) {
		if (listPersonalizationResponse == null || MapUtils.isEmpty(listPersonalizationResponse.getCardData())) {
			return null;
		}
		com.mmt.hotels.clientgateway.response.moblanding.ListPersonalizationResponse listPersonalizationResponseCG = new com.mmt.hotels.clientgateway.response.moblanding.ListPersonalizationResponse();
		listPersonalizationResponseCG.setExperimentId(listPersonalizationResponse.getExperimentId());
		listPersonalizationResponseCG.setTrackText(listPersonalizationResponse.getTrackText());
		listPersonalizationResponseCG.setCardData(buildCardData(listPersonalizationResponse.getCardData(), client,expDataMap));
		if (CollectionUtils.isEmpty(listPersonalizationResponseCG.getCardData()))
			return null;
		return listPersonalizationResponseCG;
	}

	private List<com.mmt.hotels.clientgateway.response.moblanding.CardData> buildCardData(Map<Integer, List<com.mmt.hotels.pojo.listing.personalization.CardData>> map, String client, LinkedHashMap<String,String> expDataMap) {
		if (MapUtils.isEmpty(map)) {
			return null;
		}
		List<com.mmt.hotels.clientgateway.response.moblanding.CardData> cardDataList = new ArrayList<>();
		for (Map.Entry mapElement : map.entrySet()) {
			List<com.mmt.hotels.pojo.listing.personalization.CardData> cardDataCB = (List<com.mmt.hotels.pojo.listing.personalization.CardData>) mapElement.getValue();
			com.mmt.hotels.clientgateway.response.moblanding.CardData cardDataCG = new com.mmt.hotels.clientgateway.response.moblanding.CardData();
			cardDataCG.setSequence((Integer) mapElement.getKey());
			cardDataList.add(cardDataCG);
			cardDataCG.setCardInfo(buildCardInfo(cardDataCB, client,expDataMap));
		}
		return cardDataList;
	}

	public CardInfo buildCardInfo(List<com.mmt.hotels.pojo.listing.personalization.CardData> cardDataCB, String client,LinkedHashMap<String,String> expDataMap) {
		if (CollectionUtils.isEmpty(cardDataCB)) {
			return null;
		}
		com.mmt.hotels.pojo.listing.personalization.CardData cardDataObjectCB = cardDataCB.get(0);
		CardInfo cardDataCG = new CardInfo();
		cardDataCG.setHasFilter(cardDataObjectCB.getHasFilter() != null ? cardDataObjectCB.getHasFilter() : false);
		cardDataCG.setIndex(cardDataObjectCB.getIndex());
		cardDataCG.setMinItemsToShow(cardDataObjectCB.getMinItemsToShow() == 0 ? null : cardDataObjectCB.getMinItemsToShow());
		cardDataCG.setId(cardDataObjectCB.getCardId());
		cardDataCG.setSubText(cardDataObjectCB.getSubText());
		cardDataCG.setSubType(cardDataObjectCB.getCardSubType());
		cardDataCG.setTitleText(buildTitleText(cardDataObjectCB.getCardSubType(), cardDataObjectCB.getTitleText(), expDataMap));
		cardDataCG.setHeaderUrl(cardDataObjectCB.getTierHeaderUrl());
		cardDataCG.setBorderColor(cardDataObjectCB.getBorderColor());
		cardDataCG.setCardAction(buildCardAction(cardDataObjectCB.getCardAction()));
		cardDataCG.setCampaignEndTime(cardDataObjectCB.getCampaignEndTime());
		cardDataCG.setIconTags(cardDataObjectCB.getIconTag());
		if(Constants.HIDDEN_GEM_CARD.equalsIgnoreCase(cardDataCG.getSubType())) {
			addPersuasionsForHiddenGemCard(cardDataObjectCB.getCardPayload());
		}
		cardDataCG.setCardPayload(buildCardPayload(cardDataObjectCB.getCardPayload(), client,expDataMap));
		cardDataCG.setHeading(cardDataObjectCB.getHeading());
		cardDataCG.setCardCondition(buildCardCondition(cardDataObjectCB.getCardCondition()));
		cardDataCG.setIconURL(buildIconUrl(cardDataObjectCB.getCardSubType(), cardDataObjectCB.getIconUrl(), expDataMap));
		cardDataCG.setHasAction(cardDataObjectCB.getHasAction());
		cardDataCG.setActionText(cardDataObjectCB.getActionText());
		cardDataCG.setBgImageURL(cardDataObjectCB.getBgImageUrl());
		cardDataCG.setBgGradient(cardDataObjectCB.getBgGradient());
		cardDataCG.setScratchText(cardDataObjectCB.getScratchText());
		cardDataCG.setTemplateId(cardDataObjectCB.getTemplateId());
		cardDataCG.setPageContext(cardDataObjectCB.getPageContext());
		cardDataCG.setTemplateType(cardDataObjectCB.getTemplateType());
		cardDataCG.setDescription(cardDataObjectCB.getDescription());
		cardDataCG.setBgLinearGradient(cardDataObjectCB.getBgLinearGradient());
		cardDataCG.setBorderGradient(buildBorderGradient(cardDataObjectCB.getBorderGradient()));
		cardDataCG.setHasTooltip(cardDataObjectCB.isHasToolTip());
		cardDataCG.setVideoUrl(cardDataObjectCB.getVideoUrl());
		cardDataCG.setSubTextList(cardDataObjectCB.getSubTextList());
		cardDataCG.setCardError(buildCardError(cardDataObjectCB.getCardError()));
		cardDataCG.setHotelList(buildCardHotelList(cardDataObjectCB.getHotelList()));
		cardDataCG.setDealType(cardDataObjectCB.getDealType());
		cardDataCG.setAnimatedBorderColors(cardDataObjectCB.getAnimatedBorderColors());
		cardDataCG.setClaimed(cardDataObjectCB.isClaimed());
		cardDataCG.setToggleAction(cardDataObjectCB.getToggleAction()); // toggle button on card for client to disable/enable it
		cardDataCG.setWebViewUrl(cardDataObjectCB.getWebViewUrl());
		cardDataCG.setAnimatedBorderColors(cardDataObjectCB.getAnimatedBorderColors());
		cardDataCG.setVideo(buildVideo(cardDataObjectCB.getVideoInfo()));
		cardDataCG.setBgColor(cardDataObjectCB.getBgColor());
		cardDataCG.setTextColor(cardDataObjectCB.getTextColor());
		cardDataCG.setOpenAppTitle(cardDataObjectCB.getOpenAppTitle());
		cardDataCG.setOpenAppText(cardDataObjectCB.getOpenAppText());
		cardDataCG.setInstallAppTitle(cardDataObjectCB.getInstallAppTitle());
		cardDataCG.setInstallAppText(cardDataObjectCB.getInstallAppText());
		cardDataCG.setCardImageUrl(cardDataObjectCB.getCardImageUrl());
		cardDataCG.setCardSheet(buildCardSheet(cardDataObjectCB.getCardSheet()));
		cardDataCG.setImageList(cardDataObjectCB.getImageList());
		cardDataCG.setRushDealTimerInfo(buildRushDealTimerInfo(cardDataObjectCB.getRushDealTimerInfo()));
		return cardDataCG;
	}

	private BorderGradient buildBorderGradient(com.mmt.hotels.pojo.listing.personalization.BorderGradient borderGradientHES) {
		if(borderGradientHES!=null){
			BorderGradient borderGradientCG = new BorderGradient();
			borderGradientCG.setStart(borderGradientHES.getStart());
			borderGradientCG.setCenter(borderGradientHES.getCenter());
			borderGradientCG.setEnd(borderGradientHES.getEnd());
			borderGradientCG.setDirection(borderGradientHES.getDirection());
			return borderGradientCG;
		}
		return null;
	}

	private RushDealTimerInfo buildRushDealTimerInfo(com.mmt.hotels.pojo.listing.personalization.RushDealTimerInfo rushDealTimerInfo) {
		if(rushDealTimerInfo!=null){
			RushDealTimerInfo rushDealTimerInfoCG = new RushDealTimerInfo();
			rushDealTimerInfoCG.setDesc(rushDealTimerInfo.getDesc());
			rushDealTimerInfoCG.setValidityTimestamp(rushDealTimerInfo.getValidityTimestamp());
			rushDealTimerInfoCG.setBgGradient(buildBgGradient(rushDealTimerInfo.getBgGradient()));
			return rushDealTimerInfoCG;
		}
		return null;
	}

	private com.mmt.hotels.clientgateway.response.searchHotels.BgGradient buildBgGradient(BgGradient bgGradient) {
		if(bgGradient!=null){
			com.mmt.hotels.clientgateway.response.searchHotels.BgGradient bgGradientCG = new com.mmt.hotels.clientgateway.response.searchHotels.BgGradient();
			bgGradientCG.setStart(bgGradient.getStart());
			bgGradientCG.setCenter(bgGradient.getCenter());
			bgGradientCG.setEnd(bgGradient.getEnd());
			bgGradientCG.setDirection(bgGradient.getDirection());
			return bgGradientCG;
		}
		return null;
	}

	private String buildTitleText(String subType, String titleText, LinkedHashMap<String,String> expDataMap) {
		if (Constants.MY_BIZ_ASSURED_SECTION.equalsIgnoreCase(subType) && MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(Constants.MYB_NEW_DETAILS_EXP_KEY) && Constants.TRUE.equalsIgnoreCase(expDataMap.get(Constants.MYB_NEW_DETAILS_EXP_KEY))) {
			return Constants.MYBIZ_ASSURED_NEW_TITLE;
		}
		return titleText;
	}

	private String buildIconUrl(String subType, String iconUrl, LinkedHashMap<String,String> expDataMap) {
		if (MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(Constants.MYB_NEW_DETAILS_EXP_KEY) && Constants.TRUE.equalsIgnoreCase(expDataMap.get(Constants.MYB_NEW_DETAILS_EXP_KEY))) {
			if (Constants.NON_MY_BIZ_ASSURED_SECTION.equalsIgnoreCase(subType)) {
				return Constants.NON_MYBIZ_ASSURED_NEW_ICON_URL;
			}
			else if (MY_BIZ_ASSURED_SECTION.equalsIgnoreCase(subType)) {
				return Constants.MYBIZ_ASSURED_NEW_ICON_URL;
			}
		}
		return iconUrl;
	}

	private CardSheet buildCardSheet(com.mmt.hotels.pojo.listing.personalization.CardSheet cardSheet) {
		if(cardSheet!=null){
			CardSheet cardSheetCG = new CardSheet();
			cardSheetCG.setBottomSheet(buildCardSheetElem(cardSheet.getBottomSheet()));
			cardSheetCG.setTopSheet(buildCardSheetElem(cardSheet.getTopSheet()));
			return cardSheetCG;
		}
		return null;
	}

	private CardSheetElem buildCardSheetElem(com.mmt.hotels.pojo.listing.personalization.CardSheetElem cardSheetElem) {
		if (cardSheetElem != null) {
			CardSheetElem cardSheetElemCG = new CardSheetElem();
			cardSheetElemCG.setIconUrl(cardSheetElem.getIconUrl());
			cardSheetElemCG.setText(cardSheetElem.getText());
			cardSheetElemCG.setPurpose(cardSheetElem.getPurpose());
			cardSheetElemCG.setSubText(cardSheetElem.getSubText());
			cardSheetElemCG.setStyle(buildCardSheetElemStyle(cardSheetElem.getStyle()));
			cardSheetElemCG.setSuccessInfo(buildCardSheetElemSuccessInfo(cardSheetElem.getSuccessInfo()));
			cardSheetElemCG.setCtaAction(buildCardSheetCtaAction(cardSheetElem.getCtaAction()));
			cardSheetElemCG.setInfoList(buildCardSheetInfoList(cardSheetElem.getInfoList()));
			return cardSheetElemCG;
		}

		return null;
	}

	private CardAction buildCardSheetCtaAction(com.mmt.hotels.pojo.listing.personalization.CardAction ctaAction) {
		if(ctaAction!=null){
			CardAction ctaActionCG = new CardAction();
			ctaActionCG.setTitle(ctaAction.getTitle());
			ctaActionCG.setFilters(ctaAction.getFilters());
			return ctaActionCG;
		}
		return null;
	}

	private List<GenericCardPayloadDataCG> buildCardSheetInfoList(List<GenericCardPayloadData> infoList) {
		if (CollectionUtils.isNotEmpty(infoList)) {
			List<GenericCardPayloadDataCG> cardSheetInfoListCG = new ArrayList<>();
			for (GenericCardPayloadData cardSheetInfo : infoList) {
				GenericCardPayloadDataCG cardSheetInfoCG = new GenericCardPayloadDataCG();
				cardSheetInfoCG.setTitleText(cardSheetInfo.getTitleText());
				cardSheetInfoCG.setIconUrl(cardSheetInfo.getIconUrl());
				cardSheetInfoCG.setSubText(cardSheetInfo.getSubText());
				cardSheetInfoListCG.add(cardSheetInfoCG);
			}
			return cardSheetInfoListCG;
		}
		return null;
	}

	private CardSheetElem.SuccessInfo buildCardSheetElemSuccessInfo(com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo successInfo) {
		if(successInfo!=null){
			CardSheetElem.SuccessInfo successInfoCG = new CardSheetElem.SuccessInfo();
			successInfoCG.setSubText(successInfo.getSubText());
			successInfoCG.setContent(buildCardSheetElemSuccessInfoContent(successInfo.getContent()));
			successInfoCG.setText(successInfo.getText());
			successInfoCG.setIconUrl(successInfo.getIconUrl());
			return successInfoCG;
		}
		return null;
	}

	private CardSheetElem.SuccessInfo.Content buildCardSheetElemSuccessInfoContent(com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content content) {
		if(content!=null){
			CardSheetElem.SuccessInfo.Content contentCG = new CardSheetElem.SuccessInfo.Content();
			contentCG.setText(content.getText());
			contentCG.setContentList(buildCardSheetElemSuccessInfoContentList(content.getContentList()));
			return contentCG;
		}
		return null;
	}

	private List<CardSheetElem.SuccessInfo.Content.ContentList> buildCardSheetElemSuccessInfoContentList(List<com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content.ContentList> contentList) {
		if(CollectionUtils.isNotEmpty(contentList)){
			List<CardSheetElem.SuccessInfo.Content.ContentList> contentListCG = new ArrayList<>();
			for(com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content.ContentList contentListHES : contentList){
				CardSheetElem.SuccessInfo.Content.ContentList contentListCGObject = new CardSheetElem.SuccessInfo.Content.ContentList();
				contentListCGObject.setText(contentListHES.getText());
				contentListCGObject.setIconUrl(contentListHES.getIconUrl());
				contentListCG.add(contentListCGObject);
			}
			return contentListCG;
		}
		return null;
	}

	private CardSheetElem.Style buildCardSheetElemStyle(com.mmt.hotels.pojo.listing.personalization.CardSheetElem.Style style) {
		if(style!=null) {
			CardSheetElem.Style styleCG = new CardSheetElem.Style();
			styleCG.setBorderColor(style.getBorderColor());
			return styleCG;
		}
		return null;
	}


	private Video buildVideo(MatchMakerVideoInfo videoInfo){
		if(videoInfo!=null){
			Video video = new Video();
			video.setUrl(videoInfo.getUrl());
			video.setThumbnailUrl(videoInfo.getThumbnailUrl());
			return video;
		}
		return null;
	}



	/**
	 * Method to build hidden Gem Persuasion and add to hotelEntity for Mob-Landing card.
	 */
	private void addPersuasionsForHiddenGemCard(CardPayloadResponse cardPayload) {
		if (cardPayload != null && CollectionUtils.isNotEmpty(cardPayload.getHotelList())) {
			searchHotelsFactory.getResponseService(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())).buildHiddenGemPersuasions(cardPayload.getHotelList());
		}
	}

	private CardError buildCardError(com.mmt.hotels.pojo.listing.personalization.CardError cardError){
		//Return error whenever there are no hotels available to show in card.
		logger.debug("Adding a error in SelectivePush card if present {}", cardError);
		if(cardError!=null) {
			CardError error = new CardError();
			error.setTitle(cardError.getTitle());
			error.setSubTitle(cardError.getSubTitle());
			error.setImgUrl(cardError.getImgUrl());
			return error;
		}
		return null;
	}

	private List<Hotel> buildCardHotelList(List<SearchWrapperHotelEntity> hotelList)
	{
		//Pass only required fields for hotels that are required by client
		logger.debug("Adding a hoteList in SelectivePush card if present {}", hotelList);
		if(CollectionUtils.isEmpty(hotelList))
		{
			return null;
		}
		List<Hotel> cardHotelList = new ArrayList<Hotel>();
		for(SearchWrapperHotelEntity hotelEntity : hotelList)
		{
			Hotel hotel = new Hotel();
			hotel.setName(hotelEntity.getName());
			hotel.setStarRating(hotelEntity.getStarRating());
			hotel.setId(hotelEntity.getId());
			if(hotelEntity.getDisplayFare()!=null && hotelEntity.getDisplayFare().getDisplayPriceBreakDown()!=null) {
				hotel.setPriceDetail(new PriceDetail());
				hotel.getPriceDetail().setDiscountedPriceWithTax(hotelEntity.getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice() + (hotelEntity.getDisplayFare().getDisplayPriceBreakDown().isTaxIncluded() ? 0 : hotelEntity.getDisplayFare().getDisplayPriceBreakDown().getTotalTax()));
			}
			hotel.setReviewSummary(buildCardReviewSummary(hotelEntity.getCountryCode(),
					hotelEntity.getFlyfishReviewSummary()));

			hotel.setDetailDeeplinkUrl(hotelEntity.getDetailDeeplinkUrl());
			hotel.setTotalRoomCount((hotelEntity.getDisplayFare() != null && hotelEntity.getDisplayFare().getTotalRoomCount() != null) ? hotelEntity.getDisplayFare().getTotalRoomCount() : null);
			hotel.setIsAltAcco(hotelEntity.isAltAcco());
			buildSelectiveHotelPersuasions(hotel,hotelEntity);
			cardHotelList.add(hotel);
		}

		return cardHotelList;
	}

	public void buildSelectiveHotelPersuasions(Hotel hotel, SearchWrapperHotelEntity hotelEntity){

		SelectiveHotelPersuasions cardpersuasionsHES = hotelEntity.getSelectiveHotelPersuasions();
		logger.debug("Adding a selectivePersuasion in SelectivePush card if present {}",cardpersuasionsHES);
		if(cardpersuasionsHES!=null) {
			com.mmt.hotels.clientgateway.response.searchHotels.SelectiveHotelPersuasions cardpersuasionsCG = new com.mmt.hotels.clientgateway.response.searchHotels.SelectiveHotelPersuasions();
			//copying HES CArd persuasion to CGcard persuasion as both have same nodes
			BeanUtils.copyProperties(cardpersuasionsHES, cardpersuasionsCG);
			//In noramal flow we show discount based on this condition.
			if (!enableDiscount(hotelEntity.getDisplayFare() != null ? hotelEntity.getDisplayFare().getDisplayPriceBreakDown() : null)) {
				logger.debug("Disabling Discount for selectivePersuasion in SelectivePush card ");
				cardpersuasionsCG.setDiscount(null);
			}
			hotel.setSelectiveHotelPersuasions(cardpersuasionsCG);
		}
	}

	private ReviewSummary buildCardReviewSummary(String countryCode, Map<OTA, JsonNode> reviewSummaryMap){

		logger.debug("Adding a reviewSummary in SelectivePush card if present {}, \n {} ", countryCode, reviewSummaryMap);
		if (MapUtils.isEmpty(reviewSummaryMap))
			return null;
		JsonNode ratingSummary = null;
		OTA ota = null;
		if (DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
			ratingSummary = reviewSummaryMap.get(OTA.MMT);
			ota = OTA.MMT;
		} else {
			ratingSummary = reviewSummaryMap.get(OTA.TA);
			ota = OTA.TA;
		}
		if(ratingSummary == null){
			ratingSummary = reviewSummaryMap.get(OTA.EXT);
			ota = OTA.EXT;
		}

		if (ratingSummary == null || (ratingSummary.get(CUMULATIVE_RATING_TEXT) != null && ratingSummary.get(CUMULATIVE_RATING_TEXT).intValue() == 0))
			return null;

		ReviewSummary reviewSummary = new ReviewSummary();
		reviewSummary.setSource(ota.name());

		if (ratingSummary.get(CUMULATIVE_RATING_TEXT) != null) {
			reviewSummary.setCumulativeRating(ratingSummary.get(CUMULATIVE_RATING_TEXT).floatValue());
		}

		return reviewSummary;
	}
	private CardCondition buildCardCondition(com.mmt.hotels.pojo.listing.personalization.CardCondition cardCondition) {
		if (null == cardCondition) {
			return null;
		}
		CardCondition cardConditionCG = new CardCondition();
		cardConditionCG.setCheckIfFilterApplied(buildFilter(cardCondition.getCheckIfFilterApplied()));
		cardConditionCG.setCheckIfFilterNotApplied(buildFilter(cardCondition.getCheckIfFilterNotApplied()));
		cardConditionCG.setCheckIfFilterPresent(buildFilters(cardCondition.getCheckIfFilterPresent()));
		cardConditionCG.setNoFilterApplied(cardCondition.getNoFilterApplied());
		cardConditionCG.setShouldBeFirstTimeUser(cardCondition.getShouldBeFirstTimeUser());
		cardConditionCG.setShouldBeLoggedInUser(cardCondition.getShouldBeLoggedInUser());
		cardConditionCG.setShouldBeVerifiedUser(cardCondition.getShouldBeVerifiedUser());
		cardConditionCG.setShouldCheckBrinLocked(cardCondition.getShouldCheckBrinLocked());
		cardConditionCG.setShouldCheckFC(cardCondition.getShouldCheckFC());
		cardConditionCG.setShouldCheckPAH(cardCondition.getShouldCheckPAH());
		cardConditionCG.setShouldCheckWallet(cardCondition.getShouldCheckWallet());
		return cardConditionCG;
	}

	private Set<com.mmt.hotels.clientgateway.request.Filter> buildFilters(Set<Filter> checkIfFilterPresent) {
		if (CollectionUtils.isEmpty(checkIfFilterPresent)) {
			return null;
		}
		Set<com.mmt.hotels.clientgateway.request.Filter> set = new HashSet<>();
		for (Filter filter : checkIfFilterPresent) {
			set.add(buildFilter(filter));
		}
		return set;
	}

	private com.mmt.hotels.clientgateway.request.Filter buildFilter(Filter filterHES) {
		if (null == filterHES) {
			return null;
		}
		com.mmt.hotels.clientgateway.request.Filter fltr = new com.mmt.hotels.clientgateway.request.Filter();
		fltr.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.getFilterGroupFromFilterName(filterHES.getFilterGroup().name()));
		FilterRange filterRange = null;
		if (fltr.getFilterRange() != null) {
			filterRange = new FilterRange();
			filterRange.setMaxValue(filterHES.getFilterRange().getMaxValue());
			filterRange.setMinValue(filterHES.getFilterRange().getMinValue());
		}
		fltr.setFilterRange(filterRange);
		fltr.setFilterValue(filterHES.getFilterValue());
		fltr.setRangeFilter(filterHES.isRangeFilter());
		return fltr;
	}

	private CardPayloadData buildCardPayload(CardPayloadResponse cardPayload, String client,LinkedHashMap<String,String> expDataMap) {
		if (cardPayload == null) {
			return null;
		}
		CardPayloadData cardPayloadData = new CardPayloadData();
		cardPayloadData.setDaysOfWeek(buildListOfDays(cardPayload.getDaysOfWeek()));
		cardPayloadData.setPlacementContextList(buildPlacementContextList(cardPayload));
		cardPayloadData.setSearchContext(buildSearchContext(cardPayload));
		cardPayloadData.setAltAccoData(buildAltAccoData(cardPayload.getAltAccoData()));
		cardPayloadData.setAltAccoDiscovery(buildAltAccoDiscovery(cardPayload.getAltAccoDiscovery(), client));
		cardPayloadData.setGenericCardData(buildGenericCardData(cardPayload.getGenericCardData()));
		cardPayloadData.setValueStaysDataCG(buildValueStaysData(cardPayload.getValueStaysData()));
		cardPayloadData.setHighlights(buildHighLights(cardPayload.getHighlights()));
		cardPayloadData.setHotelList(cardPayload.getHotelList());
		cardPayloadData.setContextualFilterData(buildContextualFilterData(cardPayload.getContextualFilterData()));
		cardPayloadData.setNearByData(buildNearByData(cardPayload.getNearByData()));
		cardPayloadData.setPageImageUrl(cardPayload.getPageImageUrl());
		cardPayloadData.setPageHeaderText(cardPayload.getPageHeaderText());
		cardPayloadData.setPageHeaderSubText(cardPayload.getPageHeaderSubText());
		cardPayloadData.setPolarisData(buildPolarisData(cardPayload.getPolarisData()));
		cardPayloadData.setScratchText(cardPayload.getScratchText());
		cardPayloadData.setScratchColor(cardPayload.getScratchColor());
		cardPayloadData.setTitle(cardPayload.getTitle());
		cardPayloadData.setMetaPersuasion(cardPayload.getMetaPersuasion());
		cardPayloadData.setRewardStatus(cardPayload.getRewardStatus());
		cardPayloadData.setRewardCode(cardPayload.getRewardCode());
		cardPayloadData.setFilterSelectType(cardPayload.getFilterSelectType());
		cardPayloadData.setMyPartnerHero(cardPayload.getMyPartnerHero());
		cardPayloadData.setDeeplink(cardPayload.getDeeplink());
		if(MapUtils.isNotEmpty(expDataMap) && EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(NEW_HOTEL_LIST))){ //Building new hotel list in case this Exp is True
			cardPayloadData.setHotelListNew(convertToHotelListNew(cardPayloadData));
			if(CollectionUtils.isNotEmpty(cardPayloadData.getHotelListNew()))
				cardPayloadData.setHotelList(null); //suppress oldHotelList in case new is built
		}
		cardPayloadData.setTierName(cardPayload.getTierName());
		cardPayloadData.setBenefitCardData(cardPayload.getBenefitCardData());
		List<SpokeCityCG> spokeCityCGS = buildSpokeCityData(cardPayload.getSpokeCityData());
		cardPayloadData.setSpokeCityData(spokeCityCGS);
		return cardPayloadData;
	}

	private List<Day> buildListOfDays(List<com.mmt.hotels.model.response.listpersonalization.Day> response) {
		if (CollectionUtils.isNotEmpty(response)) {
			List<Day> days = new ArrayList<>();
			for(com.mmt.hotels.model.response.listpersonalization.Day dayHES : response){
				Day day = new Day();
				day.setTitle(dayHES.getTitle());
				day.setDayDiff(dayHES.getDayDiff());
				days.add(day);
			}
			return days;
		}
		return null;
	}

	private List<AppInstallHighlight> buildHighLights(List<com.mmt.hotels.model.response.altaccodata.AppInstallHighlight> highlightsHES) {
		if (CollectionUtils.isNotEmpty(highlightsHES)) {
			List<AppInstallHighlight> highlights = new ArrayList<>();
			for(com.mmt.hotels.model.response.altaccodata.AppInstallHighlight appInstallHighlightHES : highlightsHES){
				AppInstallHighlight appInstallHighlight = new AppInstallHighlight();
				appInstallHighlight.setText(appInstallHighlightHES.getText());
				appInstallHighlight.setIconUrl(appInstallHighlightHES.getIconUrl());
				highlights.add(appInstallHighlight);
			}
			return highlights;
		}
		return null;
	}

	/**
	 * This method is used to build Spoke city collection card Data for Hub City Search HTL-37166
	 * This method is also used to build Hidden Gems Collection Card Data for Zone Search HTL-37867
	 */
	private List<SpokeCityCG> buildSpokeCityData(List<SpokeCity> spokeCities){
		List<SpokeCityCG> spokeCityCGList = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(spokeCities)){
			for(SpokeCity spokeCity: spokeCities){
				SpokeCityCG spokeCityCG = new SpokeCityCG();
				spokeCityCG.setDesc(spokeCity.getDesc());
				spokeCityCG.setType(spokeCity.getType());
				spokeCityCG.setImgURL(spokeCity.getImgURL());
				spokeCityCG.setLocId(spokeCity.getLocId());
				spokeCityCG.setLocType(spokeCity.getLocType());
				if(spokeCity.getParentLoc()!=null){
					ParentLocCG parentLocCG = new ParentLocCG();
					parentLocCG.setId(spokeCity.getParentLoc().getId());
					parentLocCG.setType(spokeCity.getParentLoc().getType());
					spokeCityCG.setParentLoc(parentLocCG);
				}
				spokeCityCG.setSearchHotelLimit(spokeCity.getSearchHotelLimit());
				spokeCityCG.setSubText2(spokeCity.getSubText2());
				spokeCityCG.setDeepLink(spokeCity.getDeeplink());
				spokeCityCG.setButtonText(spokeCity.getButtonText());
				spokeCityCGList.add(spokeCityCG);
			}
		}
		return spokeCityCGList;
	}

	public List<Hotel> convertToHotelListNew(CardPayloadData cardPayload){
		if(CollectionUtils.isNotEmpty(cardPayload.getHotelList())){
			return searchHotelsFactory.getResponseService(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())).buildPersonalizedHotels(cardPayload.getHotelList(),null,null,null,null,null, null, null);
		}
		return null;
	}

	/**
	 * This method is used to build data (e.g. starText and valueStaysPersuasion) specific to MMT ValueStays
	 *
	 */
	private ValueStaysDataCG buildValueStaysData(ValueStaysData valueStaysData) {
		ValueStaysDataCG valueStaysDataCG = null;
		if (valueStaysData != null) {
			valueStaysDataCG = new ValueStaysDataCG();
			valueStaysDataCG.setStarText(valueStaysData.getStarText());
			if (valueStaysData.getValueStaysPersuasion() != null) {
				ValueStaysPersuasionCG valueStaysPersuasionCG = new ValueStaysPersuasionCG();
				valueStaysPersuasionCG.setIconType(valueStaysData.getValueStaysPersuasion().getIconType());
				valueStaysPersuasionCG.setIconUrl(valueStaysData.getValueStaysPersuasion().getIconUrl());
				valueStaysPersuasionCG.setText(valueStaysData.getValueStaysPersuasion().getText());
				valueStaysDataCG.setValueStaysPersuasion(valueStaysPersuasionCG);
			}
		}
		return valueStaysDataCG;
	}

	private List<String> buildPlacementContextList(CardPayloadResponse cardPayloadResponse) {
		List<String> placementContextList = null;
		if (cardPayloadResponse != null && CollectionUtils.isNotEmpty(cardPayloadResponse.getPlacementContextList())) {
			placementContextList = cardPayloadResponse.getPlacementContextList()
					.stream()
					.map(AdTechPlacementContext::getContextId)
					.collect(Collectors.toList());
		}
		return placementContextList;
	}

	protected AdTechSearchContext buildSearchContext(CardPayloadResponse cardPayload) {
		AdTechSearchContext searchContext = new AdTechSearchContext();
		List<AdTechSearchContextDetails> currSearchContext = null;
		if (cardPayload.getSearchContext() != null && CollectionUtils.isNotEmpty(cardPayload.getSearchContext().getCurrSearchContext())) {
			currSearchContext = new ArrayList<>();
			for (com.mmt.hotels.model.response.adtech.AdTechSearchContextDetails searchContextDetailsHES : cardPayload.getSearchContext().getCurrSearchContext()) {
				AdTechSearchContextDetails searchContextDetails = new AdTechSearchContextDetails();
				buildSearchContextDetails(searchContextDetails, searchContextDetailsHES);
				currSearchContext.add(searchContextDetails);
			}
		}
		searchContext.setCurrSearchContext(currSearchContext);
		return searchContext;
	}

	private void buildSearchContextDetails(AdTechSearchContextDetails searchContextDetails, com.mmt.hotels.model.response.adtech.AdTechSearchContextDetails searchContextDetailsHES) {
		if (searchContextDetailsHES != null) {
			searchContextDetails.setLob(searchContextDetailsHES.getLob());
			if (searchContextDetailsHES.getDestination() != null) {
				AdTechDestination destination = new AdTechDestination();
				destination.setCityCode(searchContextDetailsHES.getDestination().getCityCode());
				destination.setCountryCode(searchContextDetailsHES.getDestination().getCountryCode());
				destination.setLocusId(searchContextDetailsHES.getDestination().getLocusId());
				destination.setLocusType(searchContextDetailsHES.getDestination().getLocusType());
				searchContextDetails.setDestination(destination);
			}
			searchContextDetails.setStartDate(searchContextDetailsHES.getStartDate());
			searchContextDetails.setEndDate(searchContextDetailsHES.getEndDate());
			searchContextDetails.setTripType(searchContextDetailsHES.getTripType());
			if (searchContextDetailsHES.getHotels() != null) {
				AdTechHotel hotel = new AdTechHotel();
				if(searchContextDetailsHES.getHotels().getRoomCount()!=null){
					hotel.setRoomCount(searchContextDetailsHES.getHotels().getRoomCount());
				}
				AdTechPaxDetails paxDetails = new AdTechPaxDetails();
				if(searchContextDetailsHES.getHotels().getPaxDetails()!=null){
					paxDetails.setAdult(searchContextDetailsHES.getHotels().getPaxDetails().getAdult());
					paxDetails.setChildren(searchContextDetailsHES.getHotels().getPaxDetails().getChildren());
					paxDetails.setChildAges(searchContextDetailsHES.getHotels().getPaxDetails().getChildAges());
				}
				hotel.setPaxDetails(paxDetails);
				searchContextDetails.setHotels(hotel);
			}
			searchContextDetails.setCorrelationKey(searchContextDetailsHES.getCorrelationKey());
			searchContextDetails.setSearchSource(searchContextDetailsHES.getSearchSource());
		}
	}

	private PolarisData buildPolarisData(com.mmt.hotels.model.response.listpersonalization.PolarisData polarisDataCB) {
		if (polarisDataCB == null) {
			return null;
		}
		PolarisData polarisDataCG = new PolarisData();
		polarisDataCG.setShowImage(polarisDataCB.isShowImage());
		polarisDataCG.setTags(buildPolarisTags(polarisDataCB.getTags()));
		polarisDataCG.setPolarisMediaInfo(buildPolarisMediaInfo(polarisDataCB.getPolarisMediaInfo()));
		return polarisDataCG;
	}

	private PolarisMediaInfo buildPolarisMediaInfo(com.mmt.hotels.model.response.listpersonalization.PolarisMediaInfo polarisMediaInfoHES) {
		if (polarisMediaInfoHES == null) return null;
		PolarisMediaInfo polarisMediaInfoCG = new PolarisMediaInfo();
		BeanUtils.copyProperties(polarisMediaInfoHES, polarisMediaInfoCG);
		return polarisMediaInfoCG;
	}

	private List<PolarisTag> buildPolarisTags(List<com.mmt.hotels.model.response.listpersonalization.PolarisTag> tags) {
		if (CollectionUtils.isEmpty(tags)) {
			return null;
		}
		List<PolarisTag> tagList = new ArrayList<>();
		for (com.mmt.hotels.model.response.listpersonalization.PolarisTag polarisTagCB : tags) {
			PolarisTag polarisTagCG = new PolarisTag();
			polarisTagCG.setId(polarisTagCB.getId());
			polarisTagCG.setDesc(polarisTagCB.getDesc());
			polarisTagCG.setImgURL(polarisTagCB.getImgURL());
			polarisTagCG.setLatitude(polarisTagCB.getLatitude());
			polarisTagCG.setLongitude(polarisTagCB.getLongitude());
			polarisTagCG.setLocId(polarisTagCB.getLocId());
			polarisTagCG.setLocType(polarisTagCB.getLocType());
			polarisTagCG.setType(polarisTagCB.getType());
			polarisTagCG.setTypeId(polarisTagCB.getTypeId());
			polarisTagCG.setShowableEntities(polarisTagCB.getShowableEntities());
			polarisTagCG.setBbox(buildBBox(polarisTagCB.getBbox()));
			polarisTagCG.setPoiCategory(polarisTagCB.getPoiCategory());
			polarisTagCG.setSubText(polarisTagCB.getSubText());
			polarisTagCG.setSubText2(polarisTagCB.getSubText2());
			polarisTagCG.setBudget(polarisTagCB.getBudget());
			polarisTagCG.setButtonText(polarisTagCB.getButtonText());
			polarisTagCG.setShortText(polarisTagCB.getShortText());
			polarisTagCG.setTag(polarisTagCB.getTag());

			tagList.add(polarisTagCG);
		}
		return tagList;
	}

	private LatLong buildBBox(BbLatLong bbox) {
		if (bbox == null) {
			return null;
		}
		LatLong bboxCG = new LatLong();
		if (bbox.getNe() != null) {
			bboxCG.setNe(new LatLongObject());
			bboxCG.getNe().setLat(bbox.getNe().getLat());
			bboxCG.getNe().setLng(bbox.getNe().getLng());
		}
		if (bbox.getSw() != null) {
			bboxCG.setSw(new LatLongObject());
			bboxCG.getSw().setLat(bbox.getSw().getLat());
			bboxCG.getSw().setLng(bbox.getSw().getLng());
		}
		return bboxCG;
	}

	private List<ContextualFilterDataCG> buildContextualFilterData(List<com.mmt.hotels.model.response.listpersonalization.ContextualFilterData> contextualFilterData) {
		if (CollectionUtils.isEmpty(contextualFilterData))
			return null;
		List<ContextualFilterDataCG> contextualFilterDataList = new ArrayList<>();
		for (com.mmt.hotels.model.response.listpersonalization.ContextualFilterData filterData : contextualFilterData) {
			ContextualFilterDataCG dataCG = new ContextualFilterDataCG();
			dataCG.setBgImageUrl(filterData.getBgImageUrl());
			dataCG.setIconUrl(filterData.getIconUrl());
			dataCG.setIndex(filterData.getIndex());
			dataCG.setHasAction(filterData.isHasAction());
			dataCG.setCardActionText(filterData.getCardActionText());
			dataCG.setCardAxis(filterData.getCardAxis());
			dataCG.setCardSubType(filterData.getCardSubType());
			dataCG.setDescText(filterData.getDescText());
			dataCG.setSelected(filterData.isSelected());
			dataCG.setHeaderText(filterData.getHeaderText());
			if (filterData.getCardFilters() != null && filterData.getCardFilters().getFilterList() != null) {
				LinkedHashMap<com.mmt.hotels.clientgateway.response.filter.FilterGroup, LinkedHashSet<com.mmt.hotels.clientgateway.response.filter.Filter>> map = new LinkedHashMap<>();
				for (FilterGroup f : filterData.getCardFilters().getFilterList().keySet()) {
					LinkedHashSet<com.mmt.hotels.clientgateway.response.filter.Filter> filterLinkedHashSet = new LinkedHashSet<>();
					filterData.getCardFilters().getFilterList().get(f).stream().forEach(filterHES -> {
						filterLinkedHashSet.add(buildFilterCG(filterHES));
					});
					map.put(com.mmt.hotels.clientgateway.response.filter.FilterGroup.valueOf(f.name()), filterLinkedHashSet);
				}
				Filters list = new Filters();
				list.setFilterList(map);
				dataCG.setCardFilters(list);
			}
			contextualFilterDataList.add(dataCG);
		}
		return contextualFilterDataList;
	}

	public com.mmt.hotels.clientgateway.response.filter.Filter buildFilterCG(Filter filterHES) {
		if(filterHES == null) {
			return null;
		}
		com.mmt.hotels.clientgateway.response.filter.Filter filterCG = new com.mmt.hotels.clientgateway.response.filter.Filter();
		if(filterHES.getFilterGroup() != null)
			filterCG.setFilterGroup(filterHES.getFilterGroup().name());
		//Add price filter only if range is available
		if ((FilterGroup.HOTEL_PRICE.equals(filterHES.getFilterGroup()) || FilterGroup.HOTEL_PRICE_BUCKET.equals(filterHES.getFilterGroup()))
				&& filterHES.getFilterRange() == null) {
			return null;
		}
		filterCG.setCount(filterHES.getCount());
		filterCG.setRangeFilter(filterHES.isRangeFilter());
		filterCG.setFilterValue(filterHES.getFilterValue());
		filterCG.setSelectedFilterText(filterHES.getSelectedFilterText());
		filterCG.setTitle(filterHES.getTitle());
		filterCG.setSubTitle(filterHES.getSubTitle());
		filterCG.setIconUrl(filterHES.getIconUrl());
		if (filterHES.getFilterRange() != null) {
			filterCG.setRangeFilter(true);
			filterCG.setFilterRange(new com.mmt.hotels.clientgateway.response.filter.FilterRange());
			filterCG.getFilterRange().setMaxValue(filterHES.getFilterRange().getMaxValue());
			filterCG.getFilterRange().setMinValue(filterHES.getFilterRange().getMinValue());
		}
		filterCG.setPreApplied(filterHES.getFilterExist());
		filterCG.setTooltip(filterHES.getTooltip());
		filterCG.setDistance(filterHES.getDistance());
		filterCG.setMatchmakerType(filterHES.getMatchmakerType());
		filterCG.setImageUrl(filterHES.getImageUrl());
		filterCG.setIconList(filterHES.getIconList());
		filterCG.setSuggestedFilters(buildSuggestedFilters(filterHES.getSuggestedFilters()));
		if(filterHES.getSequence() != null) {
			filterCG.setSequence(filterHES.getSequence());
		}

		// This flag is added at HES whenever filter title key is added and translation is to be done at CG
		// Currently being done only for INLINE HOTELS FILTER CARD
		if (filterHES.isTranslationRequired() && StringUtils.isNotEmpty(filterCG.getTitle())) {
			filterCG.setTitle(polyglotService.getTranslatedData(filterCG.getTitle()));
			if (StringUtils.isNotEmpty(filterCG.getSubTitle())) {
				filterCG.setSubTitle(polyglotService.getTranslatedData(filterCG.getSubTitle()));
			}
		}

		return filterCG;
	}

	public List<com.mmt.hotels.clientgateway.response.filter.Filter> buildSuggestedFilters(List<Filter> suggestedFilters) {
		if(CollectionUtils.isEmpty(suggestedFilters)){
			return null;
		}
		List<com.mmt.hotels.clientgateway.response.filter.Filter> suggestedFilterList = new ArrayList<>();
		suggestedFilters.forEach(filterHes -> suggestedFilterList.add(buildFilterCG(filterHes)));
		return suggestedFilterList;
	}

	private List<NearByLocation> buildNearByData(List<com.mmt.hotels.model.response.nearby.NearByLocation> nearByData) {
		if (CollectionUtils.isEmpty(nearByData))
			return null;
		List<NearByLocation> nearByDataCG = new ArrayList<>();
		for (com.mmt.hotels.model.response.nearby.NearByLocation nearData : nearByData) {
			NearByLocation nearCG = new NearByLocation();
			nearCG.setDistance(nearData.getDistance());
			nearCG.setLocId(nearData.getLocId());
			nearCG.setLocName(nearData.getLocName());
			nearCG.setLocType(nearData.getLocName());
			nearCG.setUnit(nearData.getUnit());
			nearByDataCG.add(nearCG);
		}
		return nearByDataCG;
	}

	private List<GenericCardPayloadDataCG> buildGenericCardData(List<GenericCardPayloadData> genericCardData) {
		if (CollectionUtils.isEmpty(genericCardData)) {
			return null;
		}
		List<GenericCardPayloadDataCG> genericCardPayloadDataList = new ArrayList<>();
		for (GenericCardPayloadData genericCardPayloadData : genericCardData) {
			GenericCardPayloadDataCG genericCardDataCG = new GenericCardPayloadDataCG();
			genericCardDataCG.setTitleText(genericCardPayloadData.getTitleText());
			genericCardDataCG.setText(genericCardPayloadData.getText());
			genericCardDataCG.setSubText(genericCardPayloadData.getSubText());
			genericCardDataCG.setIconUrl(genericCardPayloadData.getIconUrl());
			genericCardDataCG.setImageUrl(genericCardPayloadData.getImageUrl());
			genericCardDataCG.setVideoUrl(genericCardPayloadData.getVideoUrl());
			genericCardDataCG.setDuration(genericCardPayloadData.getDuration());
			genericCardDataCG.setItemIconType(genericCardPayloadData.getItemIconType());
			genericCardDataCG.setActionUrl(genericCardPayloadData.getActionUrl());
			genericCardDataCG.setActionText(genericCardPayloadData.getActionText());
			genericCardDataCG.setCharityDescription(genericCardPayloadData.getCharityDescription());
			genericCardDataCG.setDataCat(genericCardPayloadData.getDataCat());
			genericCardDataCG.setTag(genericCardPayloadData.getTag());
			genericCardDataCG.setGalleryView(genericCardDataCG.getGalleryView());
			genericCardDataCG.setFilterList(genericCardPayloadData.getFilterList());
			genericCardDataCG.setData(buildGenericCardData(genericCardPayloadData.getData()));
			genericCardDataCG.setPromoCode(genericCardPayloadData.getPromoCode());
			genericCardPayloadDataList.add(genericCardDataCG);
		}
		return genericCardPayloadDataList;
	}

	private List<AltAccoDiscovery> buildAltAccoDiscovery(List<CollectionsResponseBo<SearchWrapperHotelEntityAbridged>> list, String client) {
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		List<AltAccoDiscovery> altAccoDiscoveryList = new ArrayList<>();
		for (CollectionsResponseBo collectionsResponseBoCB : list) {
			AltAccoDiscovery altAccoDiscoveryCG = new AltAccoDiscovery();
			altAccoDiscoveryCG.setCollectionsResponse(buildCollectionsResponse(collectionsResponseBoCB.getCollectionsResponse(), client));
			altAccoDiscoveryList.add(altAccoDiscoveryCG);
		}
		return altAccoDiscoveryList;
	}

	private List<CollectionsResponse> buildCollectionsResponse(List<FeaturedCollections> collectionsResponse, String client) {
		if (CollectionUtils.isEmpty(collectionsResponse)) {
			return null;
		}
		List<CollectionsResponse> collectionsResponseList = new ArrayList<>();
		for (FeaturedCollections featuredCollectionsCB : collectionsResponse) {
			CollectionsResponse collectionsResponseCG = new CollectionsResponse();
			collectionsResponseCG.setAppliedFilterMap(buildAppliedFilterMap(featuredCollectionsCB.getAppliedFilterMap()));
			collectionsResponseCG.setDeepLink(featuredCollectionsCB.getDeepLink());
			collectionsResponseCG.setDeepLinkApp(featuredCollectionsCB.getDeepLinkApp());
			collectionsResponseCG.setHeading(featuredCollectionsCB.getHeading());
			// changing back to old hotels object in moblanding response as clients did not move to new object schema .
			collectionsResponseCG.setHotels(convertAbridgedIntoSearchWrapperHotelEntity(featuredCollectionsCB.getHotels()));
//            collectionsResponseCG.setHotels(searchHotelsFactory.getResponseService(client).buildPersonalizedHotels(convertAbridgedIntoSearchWrapperHotelEntity(featuredCollectionsCB.getHotels()),null, null));
			collectionsResponseCG.setPriority(featuredCollectionsCB.getPriority());
			collectionsResponseCG.setSearchContext(buildSearchContext(featuredCollectionsCB.getSearchContext()));
			collectionsResponseCG.setSubHeading(featuredCollectionsCB.getSubHeading());
			collectionsResponseCG.setThreshold(featuredCollectionsCB.getThreshold());
			collectionsResponseList.add(collectionsResponseCG);
		}
		return collectionsResponseList;
	}

	private List<com.mmt.hotels.clientgateway.response.moblanding.CardAction> buildCardAction(List<com.mmt.hotels.pojo.listing.personalization.CardAction> list) {
		if (list == null)
			return null;

		List<com.mmt.hotels.clientgateway.response.moblanding.CardAction> cardActionList = new ArrayList<>();

		for (com.mmt.hotels.pojo.listing.personalization.CardAction cardActionCB : list) {
			if (cardActionCB == null) {
				continue;
			}
			com.mmt.hotels.clientgateway.response.moblanding.CardAction cardActionCG = new com.mmt.hotels.clientgateway.response.moblanding.CardAction();
			cardActionCG.setLogin(cardActionCB.getIsLogin()); /* TO BE REMOVED */
			cardActionCG.setIsLogin(cardActionCB.getIsLogin());
			cardActionCG.setLoginWithReward(cardActionCB.getIsLoginWithReward());
			cardActionCG.setVerifyWithReward(cardActionCB.getIsVerifyWithReward());
			cardActionCG.setOpenMatchMaker(cardActionCB.getOpenMatchMaker());
			cardActionCG.setWebViewUrl(cardActionCB.getWebViewUrl());
			cardActionCG.setCategories(cardActionCB.getCategories());
			cardActionCG.setFilters(cardActionCB.getFilters());
			cardActionCG.setBreakfast(cardActionCB.getIsBreakfast());
			cardActionCG.setFreeWifi(cardActionCB.getIsFreeWifi());
			if(cardActionCB.getData()!=null){
				cardActionCG.setData(buildCardActionData(cardActionCB.getData()));
			}
			cardActionCG.setPah(cardActionCB.getIsPah());
			cardActionCG.setMatchmakerTags(buildMatchMakerTags(cardActionCB.getMatchmakerTags()));
			cardActionCG.setPriceBucket(buildPriceBucket(cardActionCB.getPriceBucket()));
			cardActionCG.setStarRating(cardActionCB.getStarRating());
			cardActionCG.setDeeplinkUrl(cardActionCB.getDeeplinkUrl());
			cardActionCG.setTitle(cardActionCB.getTitle());
			cardActionCG.setType(cardActionCB.getType());
			cardActionCG.setAp(cardActionCB.getAp());
			cardActionCG.setLocusData(cardActionCB.getLocusData());
			cardActionCG.setIconUrl(cardActionCB.getIconUrl());
			if(cardActionCB.getAction() != null){
				CardAction.MoreInfoAction action =  new CardAction.MoreInfoAction();
				action.setActionProp(cardActionCB.getAction().getActionProp());
				action.setTitle(cardActionCB.getAction().getTitle());
				cardActionCG.setAction(action);
			}
			cardActionList.add(cardActionCG);
		}
		return cardActionList;
	}

	private com.mmt.hotels.clientgateway.response.moblanding.CardActionData buildCardActionData(CardActionData cardActionDataCB){
		com.mmt.hotels.clientgateway.response.moblanding.CardActionData cardActionDataCG = new com.mmt.hotels.clientgateway.response.moblanding.CardActionData();
		cardActionDataCG.setTitle(cardActionDataCB.getTitle());
		if(CollectionUtils.isNotEmpty(cardActionDataCB.getSections())){
			cardActionDataCG.setSections(buildSections(cardActionDataCB.getSections()));
		}
		return cardActionDataCG;
	}

	private List<Section> buildSections(List<com.mmt.model.Section> sectionCB){
		List<Section> sectionList = new ArrayList<>();
		for(com.mmt.model.Section section: sectionCB){
			Section sectionCG = new Section();
			sectionCG.setTitle(section.getTitle());
			if(CollectionUtils.isNotEmpty(section.getItems())){
				sectionCG.setItems(buildItems(section.getItems()));
			}
			sectionCG.setSelection(section.getSelection());
			sectionList.add(sectionCG);
		}
		return sectionList;
	}

	private List<Item> buildItems(List<com.mmt.model.Item> items){
		List<Item> itemList = new ArrayList<>();
		for(com.mmt.model.Item item:items){
			Item itemCG = new Item();
			itemCG.setText(item.getText());
			itemCG.setType(item.getType());
			itemCG.setTextBoxTitle(item.getTextBoxTitle());
			itemCG.setCategory(item.getCategory());
			itemList.add(itemCG);
		}
		return itemList;
	}

	private PriceBucketObject buildPriceBucket(PriceBucket priceBucket) {
		if (priceBucket == null)
			return null;
		PriceBucketObject priceBucketObject = new PriceBucketObject();
		priceBucketObject.setCount(priceBucket.getCount());
		priceBucketObject.setMaxPrice(priceBucket.getMaxPrice());
		priceBucketObject.setMinPrice(priceBucket.getMinPrice());
		priceBucketObject.setText(priceBucket.getText());
		return priceBucketObject;
	}

	private List<MatchmakerTagObject> buildMatchMakerTags(List<com.mmt.hotels.model.response.listpersonalization.MatchmakerTag> list) {
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		List<MatchmakerTagObject> matchmakerTagObjectList = new ArrayList<>();
		for (com.mmt.hotels.model.response.listpersonalization.MatchmakerTag matchmakerTagCB : list) {
			MatchmakerTagObject matchmakerTagObjectCG = new MatchmakerTagObject();
			matchmakerTagObjectCG.setName(matchmakerTagCB.getName());
			matchmakerTagObjectCG.setTagId(matchmakerTagCB.getTagId());
			matchmakerTagObjectCG.setCount(matchmakerTagCB.getCount());
			matchmakerTagObjectCG.setText(matchmakerTagCB.getText());
			matchmakerTagObjectList.add(matchmakerTagObjectCG);
		}
		return matchmakerTagObjectList;
	}


	private Map<com.mmt.hotels.clientgateway.response.filter.FilterGroup, Set<FilterObject>> buildAppliedFilterMap(Map<FilterGroup, Set<Filter>> appliedFilterMap) {
		if (MapUtils.isEmpty(appliedFilterMap)) {
			return null;
		}
		Map<com.mmt.hotels.clientgateway.response.filter.FilterGroup, Set<FilterObject>> filterGroupCGSetMap = new HashMap<>();
		for (FilterGroup filterGroupCB : appliedFilterMap.keySet()) {
			com.mmt.hotels.clientgateway.response.filter.FilterGroup filterGroup = com.mmt.hotels.clientgateway.response.filter.FilterGroup.getFilterGroupFromFilterName(filterGroupCB.name());
			Set<Filter> filterSetCB = appliedFilterMap.get(filterGroupCB);
			Set<FilterObject> filterSetCG = new HashSet<>();
			for (Filter filterCB : filterSetCB) {
				if(filterCB.getFilterGroup() != null) {
					if (EXACT_ROOM_RECOMMENDATION.equalsIgnoreCase(filterCB.getFilterGroup().name()) || (BEDROOM_COUNT.equalsIgnoreCase(filterCB.getFilterGroup().name()))) {
						continue;
					}
				}
				FilterObject filterCG = new FilterObject();
				filterCG.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.valueOf(filterCB.getFilterGroup().name()));
				filterCG.setFilterRange(buildFilterRange(filterCB.getFilterRange()));
				filterCG.setFilterValue(filterCB.getFilterValue());
				filterSetCG.add(filterCG);
			}
			filterGroupCGSetMap.put(filterGroup, filterSetCG);
		}
		return filterGroupCGSetMap;
	}

	private FilterRange buildFilterRange(com.mmt.hotels.filter.FilterRange filterRange) {
		if (filterRange == null) {
			return null;
		}
		FilterRange filterRangeCG = new FilterRange();
		filterRangeCG.setMaxValue(filterRange.getMaxValue());
		filterRangeCG.setMinValue(filterRange.getMinValue());
		return filterRangeCG;
	}

	private SearchCriteria buildSearchContext(RecommendedSearchContext searchContext) {
		if (searchContext == null) {
			return null;
		}
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCheckIn(searchContext.getCheckIn());
		searchCriteria.setCheckOut(searchContext.getCheckOut());
		searchCriteria.setCityCode(searchContext.getCityCode());
		searchCriteria.setCountryCode(searchContext.getCountryCode());
		searchCriteria.setLocationId(searchContext.getLocationId());
		searchCriteria.setLocationType(searchContext.getLocationType());
		//searchCriteria.setRoomStayCandidates(buildRoomStayCandidates(searchContext.getRoomStayCandidates()));
		return searchCriteria;
	}

	private List<com.mmt.hotels.clientgateway.response.moblanding.AltAccoResponse> buildAltAccoData(List<AltAccoResponse> altAccoData) {
		if (CollectionUtils.isEmpty(altAccoData))
			return null;

		List<com.mmt.hotels.clientgateway.response.moblanding.AltAccoResponse> altAccoResponseCGList = new ArrayList<>();

		for (AltAccoResponse altAccoResponseCB : altAccoData) {

			com.mmt.hotels.clientgateway.response.moblanding.AltAccoResponse altAccoResponseCG = new com.mmt.hotels.clientgateway.response.moblanding.AltAccoResponse();

			altAccoResponseCG.setBgUrl(altAccoResponseCB.getBgUrl());
			altAccoResponseCG.setListThumbnailUrl(altAccoResponseCB.getListThumbnailUrl());
			altAccoResponseCG.setPropertyDesc(altAccoResponseCB.getPropertyDesc());
			altAccoResponseCG.setPropertyName(altAccoResponseCB.getPropertyName());
			altAccoResponseCG.setPropertyPersuasions(buildPropertyPersuations(altAccoResponseCB.getPropertyPersuasions()));
			altAccoResponseCG.setPropertyType(altAccoResponseCB.getPropertyType());
			altAccoResponseCG.setPropertyTypeList(altAccoResponseCB.getPropertyTypeList());
			altAccoResponseCG.setSequenceHeader(altAccoResponseCB.getSequenceHeader());
			altAccoResponseCG.setSequenceId(altAccoResponseCB.getSequenceId());
			altAccoResponseCG.setSequenceSubHeader(altAccoResponseCB.getSequenceSubHeader());
			altAccoResponseCG.setThumbnailUrl(altAccoResponseCB.getThumbnailUrl());

			altAccoResponseCGList.add(altAccoResponseCG);
		}
		return altAccoResponseCGList;
	}


	private List<PropertyPersuasion> buildPropertyPersuations(List<PropertyPersuasions> propertyPersuasions) {
		if (propertyPersuasions == null)
			return null;

		List<PropertyPersuasion> propertyPersuasionList = new ArrayList<>();

		for (PropertyPersuasions propertyPersuasionsCB : propertyPersuasions) {
			PropertyPersuasion propertyPersuasionCG = new PropertyPersuasion();

			propertyPersuasionCG.setDesc(propertyPersuasionsCB.getDesc());
			propertyPersuasionCG.setIconUrl(propertyPersuasionsCB.getIconUrl());
			propertyPersuasionCG.setTitle(propertyPersuasionsCB.getTitle());

			propertyPersuasionList.add(propertyPersuasionCG);
		}

		return propertyPersuasionList;

	}

	public DoubleBlackInfo getDoubleBlackInfo(DoubleBlackValidateResponse doubleBlackRsp) {
		if (doubleBlackRsp != null) {
			DoubleBlackInfo doubleBlackInfo = new DoubleBlackInfo();
			doubleBlackInfo.setBenefitId(doubleBlackRsp.getBenefitId());
			doubleBlackInfo.setBookingEligible(doubleBlackRsp.isBookingEligible());
			doubleBlackInfo.setDbFailAtPayment(doubleBlackRsp.isDbFailAtPayment());
			doubleBlackInfo.setEnrollCta(doubleBlackRsp.getEnrollCta());
			doubleBlackInfo.setMaxCurrentTier(doubleBlackRsp.getMaxCurrentTier());
			doubleBlackInfo.setMaxGreenTier(doubleBlackRsp.getMaxGreenTier());
			doubleBlackInfo.setMaxProgramTier(doubleBlackRsp.getMaxProgramTier());
			doubleBlackInfo.setMessageHeader(doubleBlackRsp.getMessageHeader());
			doubleBlackInfo.setMessageText(doubleBlackRsp.getMessageText());
			doubleBlackInfo.setMessageIcon(doubleBlackRsp.getMessageIcon());
			doubleBlackInfo.setMessageType(doubleBlackRsp.getMessageType());
			doubleBlackInfo.setMoreVerificationRequired(doubleBlackRsp.isMoreVerificationRequired());
			doubleBlackInfo.setMwPlusBalance(doubleBlackRsp.getMwPlusBalance());
			doubleBlackInfo.setNextTierMwPlus(doubleBlackRsp.getNextTierMwPlus());
			doubleBlackInfo.setRegisteredFirstName(doubleBlackRsp.getRegisteredFirstName());
			doubleBlackInfo.setRegisteredLastName(doubleBlackRsp.getRegisteredLastName());
			doubleBlackInfo.setTrackId(doubleBlackRsp.getTrackId());
			doubleBlackInfo.setUserEligible(doubleBlackRsp.isUserEligible());
			doubleBlackInfo.setUserEmail(doubleBlackRsp.getUserEmail());
			return doubleBlackInfo;
		}
		return null;
	}

	public Map<String, TotalPricing> getPriceMap(DisplayPriceBreakDown displayPriceBreakDown,
												 List<DisplayPriceBreakDown> displayPriceBreakDownList,
												 String expData, Integer roomCount, String askedCurrency,
												 String sellableType, Integer nightCount,
												 boolean isCorp, String segmentId, boolean buildToolTip, boolean groupBookingFunnel,
												 boolean groupBookingPrice, boolean ismyPartnerRequest, boolean isAltAccoHotel,
												 final MarkUpDetails markUpDetails, NoCostEmiDetails noCostEmiDetailForRootLevel,
												 List<LinkedRate> linkedRates, boolean isNewPropertyOfferApplicable, boolean isHighSellingAltAcco) {
		if (displayPriceBreakDown == null)
			return null;

		String priceDisplayMessage = (null == roomCount) ? null : getPriceDisplayMessage(expData, roomCount, sellableType, nightCount,
				groupBookingFunnel,isAltAccoHotel, isHighSellingAltAcco);
		Map<String, String > expDataMap = utility.getExpDataMap(expData);
		boolean isNewSelectRoomPage = utility.isExperimentTrue(expDataMap, NEW_SELECT_ROOM_PAGE);
		String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
		if(isNewSelectRoomPage && priceDisplayMessage.equals(Per_Night) && (client.equalsIgnoreCase(ANDROID) ||  client.equalsIgnoreCase(DEVICE_IOS))){
			priceDisplayMessage = per_night;
		}
		Map<String, TotalPricing> priceMap = new HashMap<>();
		String priceMapKey;
		boolean groupFunnelEnhancement = utility.isExperimentTrue(expDataMap, GROUP_FUNNEL_ENHANCEMENT_EXP);
		if (displayPriceBreakDown.getCouponInfo() != null) {
			priceMap.put(displayPriceBreakDown.getCouponInfo().getCouponCode(), getTotalPricing(displayPriceBreakDown, "", "", isCorp, segmentId, expData, groupBookingFunnel, null, false, markUpDetails,false,null, askedCurrency, isNewPropertyOfferApplicable));
			priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setPricingKey(displayPriceBreakDown.getPricingKey());
			priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setCouponDesc(displayPriceBreakDown.getCouponInfo().getDescription());
			priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setCouponAmount(displayPriceBreakDown.getCouponInfo().getDiscountAmount());
			priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setPriceDisplayMsg(priceDisplayMessage);
			priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setPriceTaxMsg(getShowTaxMessage(expData, roomCount, displayPriceBreakDown.getTotalTax(), askedCurrency));
			priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setEmiPlanDetail(buildEMIPlanDetails(displayPriceBreakDown.getNoCostEmiDetailsList(), noCostEmiDetailForRootLevel));
			CouponPersuasion couponPersuasion = buildCouponPersuasion(displayPriceBreakDown.getCouponInfo().getExtraDiscountType(),displayPriceBreakDown.getCouponInfo().getDiscountAmount(), displayPriceBreakDown.getDiscountPersuasionInfo(), isNewPropertyOfferApplicable);
			if (couponPersuasion != null) {
				priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setCouponPersuasion(couponPersuasion);
			}
			if (buildToolTip) priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setPriceToolTip(buildPriceToolTip(displayPriceBreakDown, nightCount, askedCurrency));
			setGroupPriceAndSavingsText(priceMap, displayPriceBreakDown.getCouponInfo().getCouponCode(), roomCount, nightCount, (long) displayPriceBreakDown.getSavingPerc(), (long) displayPriceBreakDown.getTotalAmount(), groupBookingFunnel, groupBookingPrice, ismyPartnerRequest, groupFunnelEnhancement);
			priceMapKey = displayPriceBreakDown.getCouponInfo().getCouponCode();
		} else {
			priceMap.put("DEFAULT", getTotalPricing(displayPriceBreakDown, "", "", isCorp, segmentId, expData, groupBookingFunnel, null, false, markUpDetails, false, null, askedCurrency, isNewPropertyOfferApplicable));
			priceMap.get("DEFAULT").setPricingKey(displayPriceBreakDown.getPricingKey());
			priceMap.get("DEFAULT").setPriceDisplayMsg(priceDisplayMessage);
			priceMap.get("DEFAULT").setPriceTaxMsg(getShowTaxMessage(expData, roomCount, displayPriceBreakDown.getTotalTax(), askedCurrency));
			CouponPersuasion couponPersuasion = buildCouponPersuasion(null, null, displayPriceBreakDown.getDiscountPersuasionInfo(), isNewPropertyOfferApplicable);
			if (couponPersuasion != null) {
				priceMap.get("DEFAULT").setCouponPersuasion(couponPersuasion);
			}
			if (buildToolTip) priceMap.get("DEFAULT").setPriceToolTip(buildPriceToolTip(displayPriceBreakDown, nightCount, askedCurrency));
			setGroupPriceAndSavingsText(priceMap, "DEFAULT", roomCount, nightCount, (long) displayPriceBreakDown.getSavingPerc(), (long) displayPriceBreakDown.getTotalAmount(), groupBookingFunnel, groupBookingPrice,ismyPartnerRequest, groupFunnelEnhancement);
			priceMapKey = "DEFAULT";
		}
		buildLinkedRatesPersuasions(priceMapKey,priceMap,displayPriceBreakDown,askedCurrency, linkedRates);

		if (CollectionUtils.isNotEmpty(displayPriceBreakDownList)) {
			for (DisplayPriceBreakDown displayPriceBreakDownOther : displayPriceBreakDownList) {
				if (displayPriceBreakDownOther.getCouponInfo() == null)
					continue;
				priceMap.put(displayPriceBreakDownOther.getCouponInfo().getCouponCode(), getTotalPricing(displayPriceBreakDownOther, "", "", isCorp, segmentId, expData, groupBookingFunnel, null, false, markUpDetails,false,null, askedCurrency, isNewPropertyOfferApplicable));
				priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setPricingKey(displayPriceBreakDownOther.getPricingKey());
				priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setCouponDesc(displayPriceBreakDownOther.getCouponInfo().getDescription());
				priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setCouponAmount(displayPriceBreakDownOther.getCouponInfo().getDiscountAmount());
				priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setPriceDisplayMsg(priceDisplayMessage);
				priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setPriceTaxMsg(
						getShowTaxMessage(expData, roomCount, displayPriceBreakDownOther.getTotalTax(), askedCurrency));
				priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setEmiPlanDetail(buildEMIPlanDetails(displayPriceBreakDownOther.getNoCostEmiDetailsList(), noCostEmiDetailForRootLevel));
				CouponPersuasion couponPersuasion = buildCouponPersuasion(displayPriceBreakDownOther.getCouponInfo().getExtraDiscountType(),displayPriceBreakDownOther.getCouponInfo().getDiscountAmount(), displayPriceBreakDownOther.getDiscountPersuasionInfo(), isNewPropertyOfferApplicable);
				if (couponPersuasion != null) {
					priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setCouponPersuasion(couponPersuasion);
				}
				if (buildToolTip)
					priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setPriceToolTip(buildPriceToolTip(displayPriceBreakDown, nightCount, askedCurrency));
				setGroupPriceAndSavingsText(priceMap, displayPriceBreakDownOther.getCouponInfo().getCouponCode(), roomCount, nightCount, (long) displayPriceBreakDown.getSavingPerc(), (long) displayPriceBreakDown.getTotalAmount(), groupBookingFunnel, groupBookingPrice, ismyPartnerRequest, groupFunnelEnhancement);
				priceMapKey = displayPriceBreakDownOther.getCouponInfo().getCouponCode();
				buildLinkedRatesPersuasions(priceMapKey,priceMap,displayPriceBreakDownOther,askedCurrency, linkedRates);
			}
		}
		return priceMap;
	}

    private void buildLinkedRatesPersuasions(String priceMapKey, Map<String, TotalPricing> priceMap, DisplayPriceBreakDown displayPriceBreakDown, String askedCurrency, List<LinkedRate> linkedRates) {
        try {

			if(linkedRates == null || linkedRates.isEmpty()){
				return;
			}

            for (LinkedRate linkedRate : linkedRates) {

                LinkedRatePriceCalculations linkedRatePriceCalculations;

                if (LINKEDRATE_FCNR.equalsIgnoreCase(linkedRate.getType())) {
                    if (MapUtils.isNotEmpty(displayPriceBreakDown.getLinkedRatePriceCalculationsMap())) {
						if (displayPriceBreakDown.getLinkedRatePriceCalculationsMap().containsKey(linkedRate.getPricingKey())) {
							linkedRatePriceCalculations = displayPriceBreakDown.getLinkedRatePriceCalculationsMap().get(linkedRate.getPricingKey());
						} else {
							continue;
						}
                        if (Objects.nonNull(linkedRatePriceCalculations)) {
                            String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : "INR").getCurrencySymbol();
                            String discountedPrice = utility.convertNumericValueToCommaSeparatedString(linkedRatePriceCalculations.getDisplayPriceDifference(), Locale.ENGLISH);
                            String parentOriginalPrice = utility.convertNumericValueToCommaSeparatedString(linkedRatePriceCalculations.getParentOriginalPrice(), Locale.ENGLISH);
                            TotalPricing totalPricing = priceMap.get(priceMapKey);
                            totalPricing.setLinkedRPBottomSheetTitle(polyglotService.getTranslatedData(LINKED_RATE_PLAN_BOTTOMSHEET_TITLE).replace("{discount}", discountedPrice).replace("{currency}", currencySymbol));
                            totalPricing.setLinkedRPDiscountMsg(polyglotService.getTranslatedData(LINKED_RATE_PLAN_DISCOUNT_TEXT).replace("{discount}", discountedPrice).replace("{currency}", currencySymbol));
                            totalPricing.setLinkedRPOriginalPriceMsg(polyglotService.getTranslatedData(LINKED_RATE_PLAN_ORIGINAL_PRICE_TEXT).replace("{parentOriginalPrice}", parentOriginalPrice).replace("{currency}", currencySymbol));
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error in building linked rate persuasions", e);
        }

    }

	CouponPersuasion buildCouponPersuasion(String extraDiscountType, Double discountAmount, DiscountPersuasionInfo discountPersuasionInfo, boolean isNewPropertyOfferApplicable) {
		if(isNewPropertyOfferApplicable) {
			return buildNewPropertyOfferCouponPersuasion(discountPersuasionInfo);
		} else {
			return buildFirstFiveBookingCouponPersuasion(extraDiscountType, discountAmount);
		}
	}

	CouponPersuasion buildNewPropertyOfferCouponPersuasion(@Nullable DiscountPersuasionInfo discountPersuasionInfo) {
		if(discountPersuasionInfo == null || discountPersuasionInfo.getDiscount() == 0.0 || discountPersuasionInfo.getBookingCount() == 0) {
			return null;
		}
		boolean pageContextDetail = utility.isDetailPageAPI(MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()));
		boolean desktopRequest = Utility.isDesktopRequest();

		String amount = String.valueOf((int) Math.round(discountPersuasionInfo.getDiscount()));
		String bookingCount = String.valueOf(discountPersuasionInfo.getBookingCount());

		CouponPersuasion couponPersuasion = new CouponPersuasion();
		String desc = "";
		if(pageContextDetail && desktopRequest){
			// DESKTOP and DETAIL PAGE....
			couponPersuasion.setIconType(FIRST_FIVE_BOOKING_ICON_TYPE);
			couponPersuasion.setHeading(polyglotService.getTranslatedData(FIRST_FIVE_BOOKING_HEADING));
			desc = polyglotService.getTranslatedData(PROPERTY_DISCOUNT_PERSUASION_DESC_DESKTOP);
		}else{
			desc = polyglotService.getTranslatedData(PROPERTY_DISCOUNT_PERSUASION_DESC_APPS);
			couponPersuasion.setBgColor(FIRST_FIVE_BOOKING_BG_COLOR);
		}

		if(StringUtils.isNotEmpty(desc)){
			desc = desc.replace(DISCOUNT_AMT, amount).replace(BOOKING_COUNT, bookingCount);
		}
		couponPersuasion.setText(desc);
		couponPersuasion.setIconUrl(FIRST_FIVE_BOOKING_ICON_URL);
		if(discountPersuasionInfo.getType() != null){
			couponPersuasion.setType(discountPersuasionInfo.getType());
		}

		return couponPersuasion;
	}

	private CouponPersuasion buildFirstFiveBookingCouponPersuasion(String extraDiscountType, Double discountAmount) {
		if(StringUtils.isEmpty(extraDiscountType) || discountAmount==null){
			return null;
		}
		boolean pageContextDetail = utility.isDetailPageAPI(MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()));
		boolean desktopRequest = Utility.isDesktopRequest();
		CouponPersuasion couponPersuasion = null;
		if(EXTRA_DISCOUNT_TYPE_FIRST_FIVE_BOOKING.equalsIgnoreCase(extraDiscountType)){
			// Setting Up the CouponPersuasion From the Polyglot In case of DiscountType Coming as FIVE_FIVE_BOOKING
			couponPersuasion = new CouponPersuasion();
			String desc = "";
			if(pageContextDetail && desktopRequest){
				// DESKTOP and DETAIL PAGE....
				couponPersuasion.setIconType(FIRST_FIVE_BOOKING_ICON_TYPE);
				couponPersuasion.setHeading(polyglotService.getTranslatedData(FIRST_FIVE_BOOKING_HEADING));
				desc = polyglotService.getTranslatedData(FIRST_FIVE_BOOKING_DESC_DESKTOP);
			}else{
				desc = polyglotService.getTranslatedData(FIRST_FIVE_BOOKING_DESC_APPS);
				couponPersuasion.setBgColor(FIRST_FIVE_BOOKING_BG_COLOR);
			}
			if(StringUtils.isNotEmpty(desc) && discountAmount!=null){
				desc = desc.replace(DISCOUNT_AMT, String.valueOf(discountAmount.intValue()));
			}
			couponPersuasion.setText(desc);
			couponPersuasion.setIconUrl(FIRST_FIVE_BOOKING_ICON_URL);

			return couponPersuasion;
		}
		return null;
	}

	public void getPriceDetail(TotalPricing totalPricing, DayUseRoomsResponse dayUseRoomsResponse, DayUseSlotPlan slotPlan, boolean header) {
		if(header && CollectionUtils.isNotEmpty(totalPricing.getDetails())) {
			dayUseRoomsResponse.setPriceDetail( new DayUsePriceDetail());
			for (PricingDetails priceDetail : totalPricing.getDetails()) {
				if(StringUtils.isNotEmpty(priceDetail.getKey()) && TAXES_KEY.equals(priceDetail.getKey())){
					dayUseRoomsResponse.getPriceDetail().setPriceTaxMsg(priceDetail.getLabel());
					dayUseRoomsResponse.getPriceDetail().setTotalTax(priceDetail.getAmount());
				}
				if(StringUtils.isNotEmpty(priceDetail.getKey()) && AMOUNT_LABEL_TOTAL_AMOUNT.equals(priceDetail.getKey())){
					dayUseRoomsResponse.getPriceDetail().setPriceDisplayMsg(priceDetail.getLabel());
					dayUseRoomsResponse.getPriceDetail().setTotalPrice(priceDetail.getAmount());
				}
			}
			dayUseRoomsResponse.getPriceDetail().setCouponAmount(totalPricing.getCouponAmount());
		}else{
			slotPlan.setPriceDetail(new DayUsePriceDetail());
			for (PricingDetails priceDetail : totalPricing.getDetails()) {
				if(StringUtils.isNotEmpty(priceDetail.getKey()) && TAXES_KEY.equals(priceDetail.getKey())){
					slotPlan.getPriceDetail().setPriceTaxMsg(priceDetail.getLabel());
					slotPlan.getPriceDetail().setTotalTax(priceDetail.getAmount());
				}
				if(StringUtils.isNotEmpty(priceDetail.getKey()) && AMOUNT_LABEL_TOTAL_AMOUNT.equals(priceDetail.getKey())){
					slotPlan.getPriceDetail().setPriceDisplayMsg(priceDetail.getLabel());
					double totalPrice = priceDetail.getAmount();
					if (totalPricing.getCouponAmount() > 0 && totalPrice > totalPricing.getCouponAmount()) {
						totalPrice -= totalPricing.getCouponAmount();
					}
					slotPlan.getPriceDetail().setTotalPrice(totalPrice);
				}
			}
			if(slotPlan.getPriceDetail() != null) {
				slotPlan.getPriceDetail().setTotalPrice((slotPlan.getPriceDetail().getTotalPrice() != null ? slotPlan.getPriceDetail().getTotalPrice() : 0) +
						(slotPlan.getPriceDetail().getTotalTax() != null ? slotPlan.getPriceDetail().getTotalTax() : 0));
			}
			slotPlan.getPriceDetail().setCouponAmount(totalPricing.getCouponAmount());
		}
	}

	private void setGroupPriceAndSavingsText(Map<String, TotalPricing> priceMap, String couponCode, Integer roomCount, Integer nightCount, long savingPerc, long displayPrice, boolean groupBookingFunnel, boolean groupBookingPrice, boolean ismyPartnerRequest, boolean groupFunnelEnhancement) {
		if (groupBookingFunnel) {
			numberFormatter = NumberFormat.getNumberInstance(new Locale("en", "IN"));
			String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
			//the check has been put inCase of search-roomapi not to add displayPrice in groupPriceText
			if(!groupFunnelEnhancement) {
				if (utility.isDetailPageAPI(controller) && ismyPartnerRequest) {
					priceMap.get(couponCode).setGroupPriceText(getGroupPriceText(roomCount, nightCount));
				} else {
					priceMap.get(couponCode).setGroupPriceText(getGroupPriceText(roomCount, nightCount, numberFormatter.format(displayPrice), "", false));
				}
			}
			if(savingPerc != 0.0 && groupBookingPrice) {
				priceMap.get(couponCode).setSavingsText(polyglotService.getTranslatedData(SAVING_PERC_TEXT)
						.replace("{PERCENTAGE}", String.valueOf(savingPerc)));
			}
		}
	}

	public String getGroupPriceText(Integer roomCount, Integer nightCount, String displayPrice, String client,boolean isPerNewEnabled) {
		if(nightCount == 1 && roomCount == 1) {
			return polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM, client) + (isPerNewEnabled?(UNDERSCORE + EXP_PERNEW):""))
					.replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(displayPrice))
					.replace("{NIGHT_COUNT}", String.valueOf(nightCount))
					.replace("{ROOM_COUNT}", String.valueOf(roomCount));

		} else if(nightCount == 1) {
			return polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(GROUP_PRICE_TEXT_ONE_NIGHT, client)+ (isPerNewEnabled?(UNDERSCORE + EXP_PERNEW):""))
					.replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(displayPrice))
					.replace("{NIGHT_COUNT}", String.valueOf(nightCount))
					.replace("{ROOM_COUNT}", String.valueOf(roomCount));
		} else if(roomCount == 1) {
			return polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(GROUP_PRICE_TEXT_ONE_ROOM, client)+ (isPerNewEnabled?(UNDERSCORE + EXP_PERNEW):""))
					.replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(displayPrice))
					.replace("{NIGHT_COUNT}", String.valueOf(nightCount))
					.replace("{ROOM_COUNT}", String.valueOf(roomCount));
		} else {
			return polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(GROUP_PRICE_TEXT, client)+ (isPerNewEnabled?(UNDERSCORE + EXP_PERNEW):""))
					.replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(displayPrice))
					.replace("{NIGHT_COUNT}", String.valueOf(nightCount))
					.replace("{ROOM_COUNT}", String.valueOf(roomCount));
		}
	}

	/**
	 * * GroupPriceText build method in case of MyPartner Group Funnel
	 * @param roomCount
	 * @param nightCount
	 * @return
	 */
	public String getGroupPriceText(Integer roomCount, Integer nightCount) {
		if(nightCount == 1) {
			return polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_NIGHT_WITH_TAX)
					.replace("{NIGHT_COUNT}", String.valueOf(nightCount))
					.replace("{ROOM_COUNT}", String.valueOf(roomCount));
		}else {
			return polyglotService.getTranslatedData(GROUP_PRICE_TEXT_WITH_TAX)
					.replace("{NIGHT_COUNT}", String.valueOf(nightCount))
					.replace("{ROOM_COUNT}", String.valueOf(roomCount));
		}
	}

	public EMIPlanDetail buildEMIPlanDetails(List<NoCostEmiDetails> noCostEmiDetailsList, NoCostEmiDetails noCostEmiDetailForRootLevel) {
		EMIPlanDetail emiPlanDetail = null;

		if (CollectionUtils.isNotEmpty(noCostEmiDetailsList)) {
			emiPlanDetail = buildEmiPlanDetails(noCostEmiDetailsList.get(0));
			buildNoCostEmiDetailsForRootLevel(noCostEmiDetailsList.get(0), noCostEmiDetailForRootLevel);
		}

		return emiPlanDetail;
	}

	public EMIPlanDetail buildEmiPlanDetails(NoCostEmiDetails noCostEmiDetails) {
		if (noCostEmiDetails == null || noCostEmiDetails.getEmiAmount() == 0) {
			return null;
		}

		// Format the totalCost with commas and without decimals
		EMIPlanDetail emiPlanDetail = new EMIPlanDetail();
		emiPlanDetail.setEmiTagType(EmiPlanType.NO_COST.name());
		StringBuilder emiPlanDescBuilder = new StringBuilder().append("from ")
				.append(Currency.INR.getCurrencySymbol())
				.append(numberFormatter.format(noCostEmiDetails.getEmiAmount()))
				.append("/month");
		emiPlanDetail.setEmiPlanDesc(emiPlanDescBuilder.toString());
		if (noCostEmiDetails.getCouponTiedEmiAmount() > 0) {
			// No cost EMI starting @ {currency} {amount}
			String emiTagDetail = polyglotService.getTranslatedData(NO_COST_EMI_TAG)
					.replace("{currency}", Currency.INR.getCurrencySymbol())
					.replace("{amount}", numberFormatter.format(noCostEmiDetails.getCouponTiedEmiAmount()));
			emiPlanDetail.setEmiTagDetail(emiTagDetail);
		}
		return emiPlanDetail;
	}

	private void buildNoCostEmiDetailsForRootLevel(NoCostEmiDetails noCostEmiDetails, NoCostEmiDetails noCostEmiDetailForRootLevel) {
		// noCostEmiDetailForRootLevel is initialized to new NoCostEmiDetails() in the calling method
		// so its emiAmount is 0.0 by default
		if (noCostEmiDetailForRootLevel !=  null && (noCostEmiDetailForRootLevel.getEmiAmount() == 0 || noCostEmiDetailForRootLevel.getEmiAmount() > noCostEmiDetails.getEmiAmount())) {
			noCostEmiDetailForRootLevel.setEmiAmount(noCostEmiDetails.getEmiAmount());
		}
	}

	public String buildPriceToolTip(DisplayPriceBreakDown displayPriceBreakDown, Integer nightCount, String askedCurrency) {
		if (displayPriceBreakDown == null || nightCount == null || StringUtils.isBlank(askedCurrency))
			return null;
		String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
		String msg = null;
		if (utility.isDetailPageAPI(controller)) {
			/* DisplayPrice in case of review page includes CDF, hence calculation of amount for both pages would be different. */
			if (MapUtils.isNotEmpty(displayPriceBreakDown.getOfferDiscountBreakup())
					&& displayPriceBreakDown.getOfferDiscountBreakup().containsKey(PromotionalOfferType.LOS_DEAL)
					&& displayPriceBreakDown.getOfferDiscountBreakup().get(PromotionalOfferType.LOS_DEAL) != null
					&& displayPriceBreakDown.getOfferDiscountBreakup().get(PromotionalOfferType.LOS_DEAL) > 0.0d) {
				double totalAmount = (displayPriceBreakDown.getDisplayPrice()
						+ (displayPriceBreakDown.isTaxIncluded() ? 0 : displayPriceBreakDown.getTotalTax()))
						* displayPriceBreakDown.getPricingDivisor();
				totalAmount = Utility.round(totalAmount, 0);
				String currency = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : "INR").getCurrencySymbol();
				msg = (polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TOOL_TIP))
						.replace(ConstantsTranslation.DAYS, nightCount.toString())
						.replace(ConstantsTranslation.AMOUNT, String.valueOf(totalAmount))
						.replace(ConstantsTranslation.CURRENCY, currency);

			}
		}
		return msg;
	}

	public String getShowTaxMessage(String expData, Integer roomCount, double totalTax, String askedCurrency) {
		boolean showTax = false;
		boolean wishList = false;
		if (totalTax == 0.0d) {
			showTax = false;
		} else if (expData != null) {
			Type type = new TypeToken<Map<String, String>>() {
			}.getType();
			expData = expData.replaceAll("^\"|\"$", "");
			Map<String, String> expDataMap = gson.fromJson(expData, type);
			if (expDataMap != null) {
				showTax = (("True".equalsIgnoreCase(expDataMap.get("ST")))
						|| ("t".equalsIgnoreCase(expDataMap.get("ST"))));
			}
			if (expDataMap != null && expDataMap.containsKey("IWD")) {
				wishList = "t".equalsIgnoreCase(expDataMap.get("IWD"));
			}
		}
		if (wishList) {
			Double amt = Double.valueOf(totalTax);
			return "+ " + Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : "INR").getCurrencySymbol() + " " + amt.intValue() + polyglotService.getTranslatedData(ConstantsTranslation.WISHLIST_DETAIL_TAX_AND_FEE);
		}

		if (showTax) {
			Double amt = Double.valueOf(totalTax);
			return "+ " + Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : "INR").getCurrencySymbol() + " " + amt.intValue() + polyglotService.getTranslatedData(ConstantsTranslation.TAX_AND_SERVICE_FEE);
		}
		return null;
	}

	public String getPriceDisplayMessage(String expData, Integer roomCount, String sellableType, Integer nightCount, boolean groupBookingFunnel, boolean isAltAccoHotel, boolean isHighSellingAltAcco) {
		String exp = "PN";

		if (expData != null) {
			Type type = new TypeToken<Map<String, String>>() {
			}.getType();
			expData = expData.replaceAll("^\"|\"$", "");
			Map<String, String> expDataMap = gson.fromJson(expData, type);
			if (expDataMap != null && expDataMap.containsKey("PDO")) {
				exp = expDataMap.get("PDO");
			}
		}

		if (StringUtils.isNotBlank(sellableType) && "bed".equalsIgnoreCase(sellableType)) {
			sellableType = polyglotService.getTranslatedData(ConstantsTranslation.BED_SELLABLE_TYPE);
		} else {
			sellableType = polyglotService.getTranslatedData(ConstantsTranslation.ROOM_SELLABLE_TYPE);
		}

		switch (exp) {
			case "PRN":
			case "PRNT":
				if(isAltAccoHotel)
					return polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_TEXT);
				if(groupBookingFunnel) {
					return polyglotService.getTranslatedData(ConstantsTranslation.GROUP_PER_ROOM_PER_NIGHT);
				}
				return polyglotService.getTranslatedData(ConstantsTranslation.PER_ROOM_PER_NIGHT).replace("{room}", sellableType);
			case "PN":
				if(isAltAccoHotel && !isHighSellingAltAcco)
					return polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_TEXT);
				if (roomCount != null && roomCount.intValue() == 1)
					return polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_TEXT);
				else
					return polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_FOR_NUM_ROOMS).replace("{num}", roomCount.toString()).replace("{rooms}", sellableType);


			case "PNT":
				if(isAltAccoHotel && !isHighSellingAltAcco)
					return polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_WITH_TAX);
				if (nightCount == null)
					return polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_TEXT);

				if (roomCount != null && roomCount.intValue() == 1)
					if (nightCount != null && nightCount.intValue() == 1)
						return polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_TEXT);
					else
						return polyglotService.getTranslatedData(ConstantsTranslation.FOR_NUM_ROOMS_PER_NIGHT_WITH_TAX).replace("{num}", roomCount.toString());
				else if (nightCount != null && nightCount.intValue() == 1)
					return polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_FOR_NUM_ROOMS_TEXT).replace("{num}", roomCount.toString());
				else
					return polyglotService.getTranslatedData(ConstantsTranslation.FOR_NUM_ROOMS_PER_NIGHT_WITH_TAX).replace("{num}", roomCount.toString());
			case "TP":
				if (nightCount == null)
					return polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_PRICE_TEXT);

				if (roomCount != null && roomCount.intValue() == 1)
					if (nightCount != null && nightCount.intValue() == 1)
						return polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_TEXT);
					else
						return polyglotService.getTranslatedData(ConstantsTranslation.FOR_NUM_NIGHTS).replace("{num}", nightCount.toString());
				else if (nightCount != null && nightCount.intValue() == 1){
					if(isAltAccoHotel && !isHighSellingAltAcco)
						return polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_TEXT);
					return polyglotService.getTranslatedData(ConstantsTranslation.PER_NIGHT_FOR_NUM_ROOMS_TEXT).replace("{num}", roomCount.toString());
				}
				else{
					if(isAltAccoHotel && !isHighSellingAltAcco)
						return polyglotService.getTranslatedData(ConstantsTranslation.FOR_NUM_NIGHTS).replace("{num}", nightCount.toString());
					return polyglotService.getTranslatedData(ConstantsTranslation.FOR_NUM_NIGHTS_NUM_ROOMS).replace("{num_nights}", nightCount.toString()).replace("{num_rooms}", roomCount.toString());
				}

			case "TPT":
				return polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_PRICE_TEXT);
		}
		return "";
	}

	public void updateCancelPolicyDescription(RatePlan ratePlan, List<CancelPenalty> cancelPenalty) {
		if (CollectionUtils.isNotEmpty(cancelPenalty) && cancelPenalty.get(0).getPenaltyDescription() != null
				&& cancelPenalty.get(0).getPenaltyDescription().getDescription() != null) {
			ratePlan.getCancellationPolicy().setDescription(cancelPenalty.get(0).getPenaltyDescription().getDescription());
		}
	}

	public RatePolicy getConfirmationPolicy(RoomTypeDetails roomTypeDetails) {
		RatePolicy ConfirmationPolicy = null;
		for (Map.Entry<String, RoomType> roomType : roomTypeDetails.getRoomType().entrySet()) {
			getConfirmationPolicy(roomType.getValue().getRatePlanList());
		}
		return ConfirmationPolicy;
	}


    public List<MediaInfo> buildMedia(MediaDetails mediaDetails, Map<String, String> expDataMap) {

        List<String> images = new ArrayList<>();
        List<VideoInfo> videos = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(mediaDetails.getImages())) {
            mediaDetails.getImages().forEach(imageDetails -> {
                if (imageDetails != null && StringUtils.isNotEmpty(imageDetails.getUrl())) {
                    images.add(imageDetails.getUrl());
                }
            });
        }
        if(CollectionUtils.isNotEmpty(mediaDetails.getVideos())) {
            mediaDetails.getVideos().forEach(videoDetails -> {
                VideoInfo videoInfo = buildVideoDetails(videoDetails);
                if (videoInfo != null) {
                    videos.add(videoInfo);
                }
            });
        }


        if (CollectionUtils.isEmpty(images) && CollectionUtils.isEmpty(videos)) {
            return null;
        }
        int mediaLimit = listingMediaLimit;
        if (MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(ExperimentKeys.carouselImageCount.name()) && Integer.parseInt(expDataMap.get(ExperimentKeys.carouselImageCount.name())) > 0 && Utility.isAppRequest()) {
            try {
                mediaLimit = Integer.parseInt(expDataMap.get(ExperimentKeys.carouselImageCount.name()));
            } catch (Exception e) {
                mediaLimit = listingMediaLimit;
            }
        }
//		if (StringUtils.isNotBlank(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())))
//			mediaLimitMap.getOrDefault(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()), 5);

        mediaLimit = Math.min((CollectionUtils.isNotEmpty(images) ? images.size() : 0) + (CollectionUtils.isNotEmpty(videos) ? videos.size() : 0), mediaLimit);

        List<MediaInfo> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(images)) {
            images.stream().limit(mediaLimit).forEach(e -> {
                MediaInfo m = new MediaInfo();
                m.setUrl(e);
                m.setMediaType("IMAGE");
                list.add(m);
            });
        }

        if (CollectionUtils.isNotEmpty(videos)) {
            List<MediaInfo> videoList = new ArrayList<>();
            for (VideoInfo hotelVideo : videos) {
                MediaInfo m = new MediaInfo();
                m.setTags(hotelVideo.getTags());
                m.setText(hotelVideo.getText());
                m.setThumbnailURL(hotelVideo.getThumbnailUrl());
                m.setTitle(hotelVideo.getTitle());
                m.setUrl(hotelVideo.getUrl());
                m.setMediaType("VIDEO");
                videoList.add(m);
            }

            if (CollectionUtils.isNotEmpty(videoList)) {
				/* Experiment VIDEO:0/1/2 is read from client.
				 0 (videos even if available for a hotel wont be shown on listing screen)
				 1 (the video if available for a hotel will be shown on first position)
				 2 (the video if available for a hotel will be shown on second position)
				 Also - Remove image and then add video. Total media count should be as defined in listing.hotel.media.limit
				 Hence ex 5 media - 4 images/1 video OR 5 images.
				*/
                int position = -1;
                String clientExpKey = "VIDEO";
                if (MapUtils.isNotEmpty(expDataMap)) {
                    if (expDataMap.containsKey(clientExpKey)) {
                        position = Integer.parseInt(expDataMap.get(clientExpKey));
                    }
                }

                if (position == 1 || position == 2) {
                    if (list.size() == mediaLimit)
                        list.remove(list.size() - 1);
                    list.add(position - 1, videoList.get(0));
                } else if (position != 0) {
                    list.addAll(videoList);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(list) && list.size() > mediaLimit)
            return list.subList(0, mediaLimit);
        if (CollectionUtils.isEmpty(list))
            return null;
        return list;
    }

    private VideoInfo buildVideoDetails(VideoDetails videoDetails) {
		VideoInfo video = null;
        if (videoDetails != null) {
			video = new VideoInfo();
            video.setUrl(videoDetails.getUrl());
        }
        return video;
    }


    public List<MediaInfo> buildMedia(List<String> mainImages, List<VideoInfo> videoInfos, Map<String, String> expDataMap, boolean isImageExpEnable) {
        if (CollectionUtils.isEmpty(mainImages) && CollectionUtils.isEmpty(videoInfos)) {
            return null;
        }
        int mediaLimit = listingMediaLimit;
		if(MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(ExperimentKeys.carouselImageCount.name()) && Integer.parseInt(expDataMap.get(ExperimentKeys.carouselImageCount.name())) > 0 && Utility.isAppRequest()){
            try {
                mediaLimit = Integer.parseInt(expDataMap.get(ExperimentKeys.carouselImageCount.name()));
            } catch (Exception e) {
                mediaLimit = listingMediaLimit;
            }
        }
//		if (StringUtils.isNotBlank(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())))
//			mediaLimitMap.getOrDefault(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()), 5);

		mediaLimit = Math.min((CollectionUtils.isNotEmpty(mainImages) ? mainImages.size() : 0) + (CollectionUtils.isNotEmpty(videoInfos) ? videoInfos.size() : 0), mediaLimit);

		List<MediaInfo> list = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(mainImages)) {
			mainImages.stream().limit(mediaLimit).forEach(e -> {
				MediaInfo m = new MediaInfo();
				m.setUrl(e);
				m.setMediaType("IMAGE");
				list.add(m);
			});
		}

		if (CollectionUtils.isNotEmpty(videoInfos)) {
			List<MediaInfo> videoList = new ArrayList<>();
			for (VideoInfo hotelVideo : videoInfos) {
				MediaInfo m = new MediaInfo();
				m.setTags(hotelVideo.getTags());
				m.setText(hotelVideo.getText());
				m.setThumbnailURL(hotelVideo.getThumbnailUrl());
				m.setTitle(hotelVideo.getTitle());
				m.setUrl(hotelVideo.getUrl());
				m.setMediaType("VIDEO");
				videoList.add(m);
			}

			if (CollectionUtils.isNotEmpty(videoList)) {
				/* Experiment VIDEO:0/1/2 is read from client.
				 0 (videos even if available for a hotel wont be shown on listing screen)
				 1 (the video if available for a hotel will be shown on first position)
				 2 (the video if available for a hotel will be shown on second position)
				 Also - Remove image and then add video. Total media count should be as defined in listing.hotel.media.limit
				 Hence ex 5 media - 4 images/1 video OR 5 images.
				*/
				int position = -1;
				String clientExpKey = "VIDEO";
				if (MapUtils.isNotEmpty(expDataMap)) {
					if (expDataMap.containsKey(clientExpKey)) {
						position = Integer.parseInt(expDataMap.get(clientExpKey));
					}
				}
				if (position == 0) {
					// DO not add videos
				} else if (position == 1 || position == 2) {
					if (list.size() == mediaLimit)
						list.remove(list.size() - 1);
					list.add(position - 1, videoList.get(0));
				} else {
					list.addAll(videoList);
				}
			}
		}
		if (CollectionUtils.isNotEmpty(list) && list.size() > mediaLimit)
			return list.subList(0, mediaLimit);
		if (CollectionUtils.isEmpty(list))
			return null;
		return list;
	}

	private RatePolicy getConfirmationPolicy(Map<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlanList) {
		RatePolicy confirmationPolicy = null;
		for (Map.Entry<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlan : ratePlanList.entrySet()) {
			RatePolicy ratePolicy = ratePlan.getValue().getConfirmationPolicy();
			if (confirmationPolicy == null && ratePolicy != null) {
				confirmationPolicy = ratePolicy;
				if (Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
					return confirmationPolicy;
				}
			} else if (confirmationPolicy != null && Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
				confirmationPolicy = ratePolicy;
				return confirmationPolicy;
			}
		}
		return confirmationPolicy;
	}

	private RatePolicy getConfirmationPolicy(List<RoomType> roomTypes) {
		RatePolicy confirmationPolicy = null;
		if (CollectionUtils.isNotEmpty(roomTypes)) {
			for (RoomType roomType : roomTypes) {
				getConfirmationPolicy(roomType.getRatePlanList());
			}
		}
		return confirmationPolicy;
	}

	public List<com.mmt.hotels.clientgateway.response.FacilityGroup> buildAmenities(List<FacilityGroup> facilityWithGrp, List<FacilityGroup> starFacilities, List<FacilityGroup> highlightedFacilities, boolean amendRoomHighlights) {
		if (CollectionUtils.isEmpty(facilityWithGrp))
			return null;
		List<com.mmt.hotels.clientgateway.response.FacilityGroup> amenitiesCGList = new ArrayList<>();

		Set<String> starAmenitySet = new HashSet<>();

		if (CollectionUtils.isNotEmpty(starFacilities)) {
			for (com.mmt.model.FacilityGroup facilityGroup : starFacilities) {
				if (CollectionUtils.isNotEmpty(facilityGroup.getFacilities())) {
					for (Facility facility : facilityGroup.getFacilities()) {
						starAmenitySet.add(facility.getName());
					}
				}

			}
		}

		List<com.mmt.hotels.clientgateway.response.Facility> starFacilityCGs = new ArrayList<>();
		Tuple<Boolean, FacilityGroup> isPopularWithGuestsAvailable = null;
		if (amendRoomHighlights) {
			isPopularWithGuestsAvailable = isPopularWithGuestsAvailable(highlightedFacilities);
		}
		for (com.mmt.model.FacilityGroup facilityGroup : facilityWithGrp) {
			com.mmt.hotels.clientgateway.response.FacilityGroup facility = new com.mmt.hotels.clientgateway.response.FacilityGroup();
			if (POPULAR_WITH_GUESTS.equalsIgnoreCase(facilityGroup.getName()) && isPopularWithGuestsAvailable != null && isPopularWithGuestsAvailable.getX() != null && Boolean.FALSE.equals(isPopularWithGuestsAvailable.getX()) && amendRoomHighlights) {
				continue;
			}
			facility.setName(facilityGroup.getName());
			List<com.mmt.hotels.clientgateway.response.Facility> facilityCGs = new ArrayList<>();
			for (Facility facilityHes : (isPopularWithGuestsAvailable != null && isPopularWithGuestsAvailable.getY() != null && amendRoomHighlights && POPULAR_WITH_GUESTS.equalsIgnoreCase(facilityGroup.getName())) ?
					isPopularWithGuestsAvailable.getY().getFacilities() :facilityGroup.getFacilities()) {

				com.mmt.hotels.clientgateway.response.Facility facilityCG = new com.mmt.hotels.clientgateway.response.Facility();
				facilityCG.setAttributeName(facilityHes.getAttributeName());
				facilityCG.setCategoryName(facilityHes.getCategoryName());
				facilityCG.setDisplayType(facilityHes.getDisplayType());
				facilityCG.setHighlightedName(facilityHes.getHighlightedName());
				facilityCG.setName(facilityHes.getName());
				facilityCG.setSequence(facilityHes.getSequence());
				facilityCG.setTags(facilityHes.getTags());
				facilityCG.setType(facilityHes.getType());
				if(facilityHes.getChildAttributes() != null) {
					List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG = buildChildAttributesCgFromHes(facilityHes.getChildAttributes());
					facilityCG.setChildAttributes(CollectionUtils.isNotEmpty(childAttributesCG) ? childAttributesCG : null);
				}

				if (starAmenitySet.contains(facilityHes.getName())) {
					starAmenitySet.remove(facilityHes.getName());
					starFacilityCGs.add(facilityCG);
				} else {
					facilityCGs.add(facilityCG);
				}
			}

			if (CollectionUtils.isNotEmpty(facilityCGs)) {
				facility.setFacilities(facilityCGs);
				amenitiesCGList.add(facility);
			}
		}

		if (CollectionUtils.isNotEmpty(starFacilities) && !starAmenitySet.isEmpty()) {
			for (com.mmt.model.FacilityGroup facilityGroup : starFacilities) {
				for (Facility facilityHes : facilityGroup.getFacilities()) {

					if (!starAmenitySet.contains(facilityHes.getName())) {
						continue;
					}
					com.mmt.hotels.clientgateway.response.Facility facilityCG = new com.mmt.hotels.clientgateway.response.Facility();
					facilityCG.setAttributeName(facilityHes.getAttributeName());
					facilityCG.setCategoryName(facilityHes.getCategoryName());
					facilityCG.setDisplayType(facilityHes.getDisplayType());
					facilityCG.setHighlightedName(facilityHes.getHighlightedName());
					facilityCG.setName(facilityHes.getName());
					facilityCG.setSequence(facilityHes.getSequence());
					facilityCG.setTags(facilityHes.getTags());
					facilityCG.setType(facilityHes.getType());
					if(facilityHes.getChildAttributes() != null) {
						List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG = buildChildAttributesCgFromHes(facilityHes.getChildAttributes());
						facilityCG.setChildAttributes(CollectionUtils.isNotEmpty(childAttributesCG) ? childAttributesCG : null);
					}
					starFacilityCGs.add(facilityCG);
				}
			}

		}

		if (CollectionUtils.isNotEmpty(starFacilityCGs)) {
			Collections.sort(starFacilityCGs,comparingInt(starFacilityCG -> (starFacilityCG.getSequence() == null ? Integer.MAX_VALUE : starFacilityCG.getSequence())));
			com.mmt.hotels.clientgateway.response.FacilityGroup facility = new com.mmt.hotels.clientgateway.response.FacilityGroup();
			facility.setName(polyglotService.getTranslatedData(ConstantsTranslation.STAR_FACILITIES));
			facility.setType(Constants.BOLD_TYPE);
			facility.setFacilities(starFacilityCGs);
			amenitiesCGList.add(0, facility);
		}


		return amenitiesCGList;
	}

	private Tuple<Boolean, com.mmt.model.FacilityGroup> isPopularWithGuestsAvailable(List<FacilityGroup> highlightedFacilities) {
		if (CollectionUtils.isNotEmpty(highlightedFacilities)) {
			for (com.mmt.model.FacilityGroup facilityGroup : highlightedFacilities) {
				if (StringUtils.equalsIgnoreCase(facilityGroup.getName(), POPULAR_WITH_GUESTS) && CollectionUtils.isNotEmpty(facilityGroup.getFacilities())) {
					return new Tuple<>(true, facilityGroup);
				}
			}
		}
		return new Tuple<>(false, null);
	}

	public List<String> buildHighlightedAmenities(Set<String> highlights) {
		if (CollectionUtils.isEmpty(highlights))
			return null;
		List<String> highlightedAmenities = new ArrayList<>();
		for (String h : highlights) {
			highlightedAmenities.add(h);
		}
		return highlightedAmenities;
	}

	public ReviewSummary buildReviewSummary(String countryCode, Map<OTA, JsonNode> reviewSummaryMap, boolean combinedOTASupported) {

		if (MapUtils.isEmpty(reviewSummaryMap))
			return null;
		JsonNode ratingSummary = null;
		OTA ota = null;
		if (Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
			ratingSummary = reviewSummaryMap.get(OTA.MMT);
			ota = OTA.MMT;
		} else {
			ota = getOtaForInternationalHotels(reviewSummaryMap);
			ratingSummary = reviewSummaryMap.get(ota);
		}
		if(ratingSummary == null){
			ratingSummary = reviewSummaryMap.get(OTA.EXT);
			ota = OTA.EXT;
		}

		if (ratingSummary == null || (ratingSummary.get("cumulativeRating") != null && ratingSummary.get("cumulativeRating").intValue() == 0))
			return null;
		ReviewSummary reviewSummary = new ReviewSummary();

		if (!combinedOTASupported &&
				(OTA.BKG.name().equalsIgnoreCase(ota.name()) || OTA.MMT_BKG.name().equalsIgnoreCase(ota.name()) || OTA.MMT_EXP.name().equalsIgnoreCase(ota.name()))) {
			reviewSummary.setSource(OTA.MMT.name());
		} else  {
			reviewSummary.setSource(ota.name());
		}

		if (ratingSummary.get("cumulativeRating") != null) {
			reviewSummary.setCumulativeRating(ratingSummary.get("cumulativeRating").floatValue());
		}
		if (ratingSummary.get("totalRatingCount") != null) {
			reviewSummary.setTotalRatingCount(ratingSummary.get("totalRatingCount").intValue());
		}
		if (ratingSummary.get("totalReviewsCount") != null) {
			reviewSummary.setTotalReviewCount(ratingSummary.get("totalReviewsCount").intValue());
		}
		if (ratingSummary.get("disableLowRating") != null) {
			reviewSummary.setDisableLowRating(ratingSummary.get("disableLowRating").booleanValue());
		}
		if (ratingSummary.get("preferredOTA") != null) {
			reviewSummary.setPreferredOTA(ratingSummary.get("preferredOTA").booleanValue());
		}
		JsonNode travellerRatingSummary = ratingSummary.get("travellerRatingSummary");
		if (travellerRatingSummary != null) {
			reviewSummary.setHotelRatingSummary(getHotelRatingSummary(objectMapperUtil.getObjectFromJsonNode(travellerRatingSummary, new TypeReference<TravellerRatingSummaryDTO>() {
			})));
			SeekTagDetails seekTagDetails = new SeekTagDetails();
			boolean isSeekTagsEmpty = true;
			if(travellerRatingSummary.get("seekTagsTitle") != null) {
				seekTagDetails.setSeekTagsTitle(travellerRatingSummary.get("seekTagsTitle").asText());
				isSeekTagsEmpty = false;
			}
			if(travellerRatingSummary.get("seekTagsSubtitle") != null) {
				seekTagDetails.setSeekTagsSubtitle(travellerRatingSummary.get("seekTagsSubtitle").asText());
				isSeekTagsEmpty = false;
			}
			if(travellerRatingSummary.get("defaultSeekTagCount") != null) {
				seekTagDetails.setDefaultSeekTagCount(travellerRatingSummary.get("defaultSeekTagCount").asInt());
				isSeekTagsEmpty = false;
			}
			if(travellerRatingSummary.get("maxSeekTagCount") != null) {
				seekTagDetails.setMaxSeekTagCount(travellerRatingSummary.get("maxSeekTagCount").asInt());
				isSeekTagsEmpty = false;
			}
			if(!isSeekTagsEmpty) {
				reviewSummary.setSeekTagDetails(seekTagDetails);
			}
		}
		if (ratingSummary.get("ratingText") != null) {
			reviewSummary.setRatingText(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("ratingText"), new TypeReference<String>() {
			}));
		}
		if (ratingSummary.get("additionalInfo") != null) {
			reviewSummary.setAdditionalInfo(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("additionalInfo"), new TypeReference<FlyfishReviewAdditionalInfo>() {
			}));
		}

		if (ota == OTA.EXT) {
			reviewSummary.setDisclaimer(ratingSummary.get("disclaimer"));
			reviewSummary.setReviewHighlights(ratingSummary.get("reviewHighlights"));
			reviewSummary.setReviewHighlightTitle(ratingSummary.get("reviewHighlightTitle"));
		}

		return reviewSummary;
	}

	public ReviewSummary buildPfmReviewSummary(String countryCode, com.mmt.hotels.model.response.flyfish.ReviewSummary pfmReviewSummary){
		try {
			ReviewSummary reviewSummary = new ReviewSummary();
			reviewSummary.setRatingText(pfmReviewSummary.getRatingText());
			reviewSummary.setCumulativeRating((float) pfmReviewSummary.getHotelRating());
			reviewSummary.setTotalReviewCount(pfmReviewSummary.getReviewCount());
			reviewSummary.setTotalRatingCount(pfmReviewSummary.getRatingCount());
			reviewSummary.setHotelRatingSummary(pfmReviewSummary.getTopicRatings().stream().map(topicWiseRatings -> {
				ConceptSummary conceptSummary = new ConceptSummary();
				conceptSummary.setReviewCount(topicWiseRatings.getReviewCount());
				conceptSummary.setDisplayText(topicWiseRatings.getTitle());
				conceptSummary.setValue(topicWiseRatings.getRating());
				conceptSummary.setShow(topicWiseRatings.isShow());
				return conceptSummary;
			}).collect(Collectors.toList()));
			return reviewSummary;
		}
		catch(Exception e){
			logger.error("Error in building review summary for PFM", e);
			return null;
		}
	}

	private OTA getOtaForInternationalHotels(Map<OTA, JsonNode> reviewSummaryMap) {
		OTA ota = OTA.TA; //default for IH hotels
		for (Map.Entry<OTA, JsonNode> reviewSummary : reviewSummaryMap.entrySet()) {
			JsonNode preferredOTA = reviewSummaryMap.get(reviewSummary.getKey()).get("preferredOTA");
			if (preferredOTA != null && preferredOTA.booleanValue()) {
				ota = reviewSummary.getKey();
				break;
			}
		}

		return ota;
	}

	private CountryWiseReviewData buildCountryWiseDataInternal(int reviewCount, String country, Map<String, String> nationalityMap) {
		String nationality = nationalityMap.get(country);

		if (reviewCount > MIN_COUNTRY_WISE_REVIEW_COUNT && StringUtils.isNotEmpty(nationality)) {
			String reviewText = polyglotService.getTranslatedData(COUNTRY_WISE_REVIEW_TEXT);

			if (StringUtils.isNotEmpty(reviewText)) {
				reviewText = reviewText.replace(REVIEW_COUNT, Integer.toString(reviewCount)).replace(NATIONALITY, nationality);
			} else {
				return null;
			}

			CountryWiseReviewData countryWiseReviewData = new CountryWiseReviewData();
			countryWiseReviewData.setText(reviewText);
			countryWiseReviewData.setColor(COUNTRY_WISE_REVIEW_TEXT_COLOR);
			return countryWiseReviewData;
		}
		return null;
	}

	public CountryWiseReviewData buildCountryWiseData(ListingReviewDetails userReviewSummary) {
		if (userReviewSummary != null && userReviewSummary.getCountryWiseReviewCount() != null) {
			return buildCountryWiseDataInternal(
					userReviewSummary.getCountryWiseReviewCount().getReviewCount(),
					userReviewSummary.getCountryWiseReviewCount().getCountry(),
					NATIONALITY_MAP
			);
		}
		return null;
	}

	public CountryWiseReviewData buildCountryWiseData(com.mmt.hotels.model.response.flyfish.ReviewSummary userReviewSummary) {
		if (userReviewSummary != null && userReviewSummary.getCountryWiseReviewCount() != null) {
			return buildCountryWiseDataInternal(
					userReviewSummary.getCountryWiseReviewCount().getReviewCount(),
					userReviewSummary.getCountryWiseReviewCount().getCountry(),
					NATIONALITY_MAP
			);
		}
		return null;
	}

    public ReviewSummary buildReviewSummary(ListingReviewDetails userReviewSummary, boolean combinedOTASupported) {
        if (userReviewSummary == null) {
            return null;
        }
        ReviewSummary reviewSummary = new ReviewSummary();
        reviewSummary.setCumulativeRating((float) userReviewSummary.getRating());
        reviewSummary.setTotalReviewCount(userReviewSummary.getTotalReviewCount());
        reviewSummary.setTotalRatingCount(userReviewSummary.getTotalRatingCount());
        reviewSummary.setRatingText(userReviewSummary.getRatingText());
        if (StringUtils.isNotBlank(userReviewSummary.getOta())) {
            reviewSummary.setPreferredOTA(true);
			if (!combinedOTASupported && (
					OTA.BKG.name().equalsIgnoreCase(userReviewSummary.getOta()) || OTA.MMT_BKG.name().equalsIgnoreCase(userReviewSummary.getOta()) || OTA.MMT_EXP.name().equalsIgnoreCase(userReviewSummary.getOta())
			)) {
				reviewSummary.setSource(OTA.MMT.name());
			} else  {
				reviewSummary.setSource(userReviewSummary.getOta().toUpperCase());
			}
        }

        if (CollectionUtils.isNotEmpty(userReviewSummary.getSubRatings())) {
            reviewSummary.setHotelRatingSummary(userReviewSummary.getSubRatings().stream().map(topicWiseRatings -> {
                ConceptSummary conceptSummary = new ConceptSummary();
                conceptSummary.setReviewCount(topicWiseRatings.getReviewCount());
                conceptSummary.setDisplayText(topicWiseRatings.getName());
                conceptSummary.setValue(topicWiseRatings.getRating());
                conceptSummary.setShow(topicWiseRatings.isShow());
                return conceptSummary;
            }).collect(Collectors.toList()));
        }

		CountryWiseReviewData countryWiseReviewData = buildCountryWiseData(userReviewSummary);
		reviewSummary.setCountryWiseReviewData(countryWiseReviewData);

        return reviewSummary;
    }

    public ReviewSummary buildReviewSummary(com.mmt.hotels.model.response.flyfish.ReviewSummary userReviewSummary, boolean combinedOTASupported) {
        if (userReviewSummary == null) {
            return null;
        }

		ReviewSummary reviewSummary = new ReviewSummary();
		reviewSummary.setCumulativeRating((float) userReviewSummary.getHotelRating());
		reviewSummary.setTotalReviewCount(userReviewSummary.getReviewCount());
		reviewSummary.setTotalRatingCount(userReviewSummary.getRatingCount());
		reviewSummary.setRatingText(userReviewSummary.getRatingText());
		if (StringUtils.isNotBlank(userReviewSummary.getOta())) {
			reviewSummary.setPreferredOTA(true);
			if (!combinedOTASupported && (
					OTA.BKG.name().equalsIgnoreCase(userReviewSummary.getOta()) ||
					OTA.MMT_BKG.name().equalsIgnoreCase(userReviewSummary.getOta()) || OTA.MMT_EXP.name().equalsIgnoreCase(userReviewSummary.getOta())
			)) {
				reviewSummary.setSource(OTA.MMT.name());
			} else  {
				reviewSummary.setSource(userReviewSummary.getOta().toUpperCase());
			}
		}

		CountryWiseReviewData countryWiseReviewData = buildCountryWiseData(userReviewSummary);
		reviewSummary.setCountryWiseReviewData(countryWiseReviewData);

		reviewSummary.setSelectedCategory(userReviewSummary.getSelectedCohort());
		if(reviewSummary.getSelectedCategory() != null && reviewSummary.getSelectedCategory().equals(ReviewCategoryConstants.BUSINESS.name())){
			String businessRatingHoverText = polyglotService.getTranslatedData(BUSINESS_RATING_HOVER_TEXT);
			if(StringUtils.isNotEmpty(businessRatingHoverText)){
				reviewSummary.setRatingHoverText(businessRatingHoverText);
			}
			reviewSummary.setSelectedCategoryIcon(businessRatingIconUrl);
		}

		if (CollectionUtils.isNotEmpty(userReviewSummary.getTopicRatings())) {
			reviewSummary.setHotelRatingSummary(userReviewSummary.getTopicRatings().stream().map(topicWiseRatings -> {
				ConceptSummary conceptSummary = new ConceptSummary();
				conceptSummary.setReviewCount(topicWiseRatings.getReviewCount());
				conceptSummary.setDisplayText(topicWiseRatings.getTitle());
				conceptSummary.setValue(topicWiseRatings.getRating());
				conceptSummary.setShow(topicWiseRatings.isShow());
				return conceptSummary;
			}).collect(Collectors.toList()));
		}

		return reviewSummary;
	}

	public List<ConceptSummary> getHotelRatingSummary(TravellerRatingSummaryDTO travellerRatingSummaryDTO) {
		if (travellerRatingSummaryDTO == null || CollectionUtils.isEmpty(travellerRatingSummaryDTO.getHotelSummary())) {
			return null;
		}

		List<ConceptSummary> hotelratingSummary = new ArrayList<>();
		for (ConceptSummaryDTO conceptSummaryDTO : travellerRatingSummaryDTO.getHotelSummary()) {
			ConceptSummary conceptSummary = new ConceptSummary();
			conceptSummary.setConcept(conceptSummaryDTO.getConcept());
			conceptSummary.setHeroTag(conceptSummaryDTO.isHeroTag());
			conceptSummary.setReviewCount(conceptSummaryDTO.getReviewCount());
			conceptSummary.setShow(conceptSummaryDTO.getShow());
			conceptSummary.setSubConcepts(buildSubConcepts(conceptSummaryDTO.getSubConcepts()));
			conceptSummary.setValue(conceptSummaryDTO.getValue());
			conceptSummary.setDisplayText(conceptSummaryDTO.getDisplayText());
			hotelratingSummary.add(conceptSummary);
		}
		return hotelratingSummary;
	}

	private List<SubConcept> buildSubConcepts(List<SubConceptDTO> subConceptDTOList) {
		List<SubConcept> subConcepts = null;
		if (CollectionUtils.isNotEmpty(subConceptDTOList)) {
			subConcepts = new ArrayList<>();
			for (SubConceptDTO subConceptDTO : subConceptDTOList) {
				SubConcept subConcept = new SubConcept();
				subConcept.setPriorityScore(subConceptDTO.getPriorityScore());
				subConcept.setRelatedReviewCount(subConceptDTO.getRelatedReviewCount());
				subConcept.setSentiment(subConceptDTO.getSentiment());
				subConcept.setSubConcept(subConceptDTO.getSubConcept());
				subConcept.setTagType(subConceptDTO.getTagType());
				subConcept.setDisplayText(subConceptDTO.getDisplayText());
				subConcept.setSource(subConceptDTO.getSource());
				subConcepts.add(subConcept);
			}
		}
		return subConcepts;
	}

	public GeoLocation buildGeoLocation(com.mmt.hotels.model.response.searchwrapper.GeoLocation geoLocation) {
		if (geoLocation == null)
			return null;
		GeoLocation geoLocationCG = new GeoLocation();
		geoLocationCG.setDistance(geoLocation.getDistanceMeter());
		geoLocationCG.setLatitude(StringUtils.isNotBlank(geoLocation.getLatitude()) ? Double.valueOf(geoLocation.getLatitude()) : null);
		geoLocationCG.setLongitude(StringUtils.isNotBlank(geoLocation.getLongitude()) ? Double.valueOf(geoLocation.getLongitude()) : null);
		return geoLocationCG;
	}

    public GeoLocation buildGeoLocation(GeoLocationDetails geoLocation) {
        if (geoLocation == null)
            return null;
        GeoLocation geoLocationCG = new GeoLocation();
        //geoLocationCG.setDistance(geoLocation.getDistanceMeter());
        geoLocationCG.setLatitude(StringUtils.isNotBlank(geoLocation.getLatitude()) ? Double.valueOf(geoLocation.getLatitude()) : null);
        geoLocationCG.setLongitude(StringUtils.isNotBlank(geoLocation.getLongitude()) ? Double.valueOf(geoLocation.getLongitude()) : null);
        return geoLocationCG;
    }

    public List<Poi> getPois(List<POIInfo> poiList) {
        if (CollectionUtils.isEmpty(poiList)) return null;
        List<Poi> pois = new LinkedList<>();
        for (POIInfo poiInfoHES : poiList) {
            Poi poi = new Poi();
            BeanUtils.copyProperties(poiInfoHES, poi);
            BeanUtils.copyProperties(poiInfoHES.getCentre(), poi.getCentre());
            if (null != poiInfoHES.getMeta()) {
                poi.setMeta(new MetaInfo());
                BeanUtils.copyProperties(poiInfoHES.getMeta(), poi.getMeta());
                poi.getMeta().setRanking(poiInfoHES.getMeta().getRanking());
                if (poiInfoHES.getMeta().getCategory() != null && StringUtils.isNotBlank(poiInfoHES.getMeta().getCategory().getName())) {
                    poi.getMeta().setCategory(poiInfoHES.getMeta().getCategory().getName());
                }
            }
            pois.add(poi);
        }
        return pois;
    }

	public String getCorporateSegmentId(RoomTypeDetails roomTypeDetails) {
		if (null == roomTypeDetails) return "";
		return getCorporateSegmentId(roomTypeDetails.getRoomType().values().stream().collect(Collectors.toList()));
	}

	public String getCorporateSegmentId(List<RoomType> roomTypes) {
		String corpSegmentId = "";
		if (CollectionUtils.isEmpty(roomTypes)) return "";
		Optional<com.mmt.hotels.model.response.pricing.RatePlan> corpRatePlan = roomTypes.stream().map(roomType -> roomType.getRatePlanList().values()).
				flatMap(Collection::stream).filter(ratePlan -> corpSegments.contains(ratePlan.getSegmentId())).findFirst();
		if (corpRatePlan.isPresent()) return corpRatePlan.get().getSegmentId();
		return corpSegmentId;
	}

	public List<SearchWrapperHotelEntity> convertAbridgedIntoSearchWrapperHotelEntity(List<SearchWrapperHotelEntity> list) {
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		List<SearchWrapperHotelEntity> newRespList = new ArrayList<>();
		for (SearchWrapperHotelEntityAbridged abridged : list) {
			SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
			BeanUtils.copyProperties(abridged, searchWrapperHotelEntity);
			newRespList.add(searchWrapperHotelEntity);
		}
		return newRespList;
	}

	public LinkedHashMap<String, PersuasionResponse> buildSafetyPersuasionList(List<String> categories) {


		if (CollectionUtils.isEmpty(categories)) {
			return null;
		}

		LinkedHashMap<String, PersuasionResponse> persuasionList = new LinkedHashMap<>();

		if (StringUtils.isNotBlank(mySafetyDataPolyglot)) {

			try {

				persuasionResponseMap = objectMapperUtil.getObjectFromJsonWithType(mySafetyDataPolyglot,
						new TypeReference<Map<String, Map<String, PersuasionResponse>>>() {
						}, DependencyLayer.CLIENTGATEWAY);

				polyglotHelper.translatePersuasionMap(persuasionResponseMap);

				Map<String, PersuasionResponse> persuasionMap = persuasionResponseMap.get("mySafetyData");

				for (String category : categories) {

					if (persuasionMap.containsKey(category)) {

						persuasionList.put(category, persuasionMap.get(category));
					}
				}

			} catch (Exception e) {
				logger.warn("Error in buildSafetyPersuasionList");
			}


		}
		return persuasionList;

	}


	public Map<String, HotelTag> buildValueStaysHotelTag(String tagTitle, Map<String, HotelTag> hotelTagMap) {
		if(MapUtils.isEmpty(hotelTagMap)) {
			hotelTagMap = new HashMap<>();
		}
		HotelTag hotelTag = new HotelTag();
		hotelTag.setTitle(polyglotService.getTranslatedData(tagTitle));
		hotelTag.setBackground(valueStayBackground);
		hotelTag.setIcon(Utility.isGccOrKsa() ? iconUrlGcc : iconUrl);
		hotelTag.setType(HotelTagType.VALUE_STAYS.getValue());
		hotelTagMap.put("PC_HOTEL_TOP", hotelTag);
		return hotelTagMap;
	}

	public LinkedHashMap<String, Map<String, HotelCategoryData>> buildHotelCategoryDataMap(List<String> categories, boolean enableThemefication) {
		if (CollectionUtils.isEmpty(categories)) {
			return null;
		}
		LinkedHashMap<String, Map<String, HotelCategoryData>> applicableHotelCategoryData = new LinkedHashMap<>();
		Map<String, Map<String, HotelCategoryData>> hotelCategoryDataMapModified = new HashMap<>();
		try {
			String jsonAsStringMapping = gson.toJson(enableThemefication ? hotelCategoryDataMapV2 : hotelCategoryDataMap);
			if (StringUtils.isNotBlank(jsonAsStringMapping)) {
				hotelCategoryDataMapModified = objectMapperUtil.getObjectFromJsonWithType(jsonAsStringMapping,
						new TypeReference<Map<String, Map<String, HotelCategoryData>>>() {
						}, DependencyLayer.CLIENTGATEWAY);
			}
		} catch (Exception e) {
			logger.warn("Error in building hotelCategoryDataMapModified");
		}


		if (MapUtils.isNotEmpty(hotelCategoryDataMapModified)) {
			for (String category : categories) {
				if (!CORP_ID_CONTEXT.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue())) && MyBiz_Assured.equalsIgnoreCase(category)) {
					continue;
				}
				if (hotelCategoryDataMapModified.containsKey(category)) {
					polyglotHelper.translateHotelCategoryDataMap(hotelCategoryDataMapModified.get(category));
					applicableHotelCategoryData.put(category, hotelCategoryDataMapModified.get(category));

				}
			}
		}
		return applicableHotelCategoryData;
	}

	public LinkedHashMap<String, HotelCategoryDataWeb> buildHotelCategoryDataWeb(List<String> categories) {
		if (CollectionUtils.isEmpty(categories)) {
			return null;
		}
		LinkedHashMap<String, HotelCategoryDataWeb> applicableHotelCategoryDataWeb = new LinkedHashMap();

		try {

			String jsonAsStringMapping = gson.toJson(hotelCategoryDataWebMapNew);
			if (StringUtils.isNotBlank(jsonAsStringMapping)) {
				hotelCategoryDataWebMapModified = objectMapperUtil.getObjectFromJsonWithType(jsonAsStringMapping,
						new TypeReference<Map<String, HotelCategoryDataWeb>>() {
						}, DependencyLayer.CLIENTGATEWAY);
			}

		} catch (Exception e) {
			logger.warn("Error in building hotelCategoryDataMapModified");
		}

		if (MapUtils.isNotEmpty(hotelCategoryDataWebMapModified)) {
			for (String category : categories) {
				if (!CORP_ID_CONTEXT.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue())) && MyBiz_Assured.equalsIgnoreCase(category)) {
					continue;
				}
				if (hotelCategoryDataWebMapModified.containsKey(category)) {
					polyglotHelper.translateHotelCategoryDataWebMapNew(hotelCategoryDataWebMapModified.get(category));
					applicableHotelCategoryDataWeb.put(category, hotelCategoryDataWebMapModified.get(category));

				}
			}
		}
		return applicableHotelCategoryDataWeb;
	}
	public MyPartnerPersonalizedSection getMyPartnerControlOnRCRB(Map<String, String> expDataMap) {
		/*
		 * returns MyPartnerControlPersonalisationKey by extracting mypartner_control_RB_RC from expDataMap
		 * */
		if (MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(MYPARTNER_CONTROL_RB_RC))
			return MyPartnerPersonalizedSection.valueOf(expDataMap.get(MYPARTNER_CONTROL_RB_RC));
		return MyPartnerPersonalizedSection.RB_RC_DEFAULT;
	}
	public boolean enableSaveValue(Map<String, String> expDataMap) {
		/*
		 * return true if experiment disableSaveValue is not 1
		 * */
		if (MapUtils.isNotEmpty(expDataMap)) {
			if(expDataMap.containsKey(HOMESTAY_PERSUASION_ALC)
					&& expDataMap.get(HOMESTAY_PERSUASION_ALC).equalsIgnoreCase("T"))
				return false;
			return MapUtils.isNotEmpty(expDataMap) &&
					expDataMap.containsKey(AB_EXPT_DISABLE_SAVE_VALUE) && Integer.parseInt(expDataMap.get(AB_EXPT_DISABLE_SAVE_VALUE)) != 1;
		}
		return false;
	}

	public boolean enableAmenitiesPersuasion(Map<String, String> expDataMap, String funnelSource,boolean isMyPartnerRequest) {
		/*
		 * returns true if amenities persuasion is to be shown
		 * on the basis of experiment disableAmenities
		 * 0 - show amenities, 1 - don't show amenities, 2 - contextual amenities
		 * */

		if(MapUtils.isNotEmpty(expDataMap)) {
			if (TRUE.equalsIgnoreCase(expDataMap.get(CONTEXTUALISATION_ENABLE_EXP)))
				return false;
			if (FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)
					&& expDataMap.containsKey(HOMESTAY_PERSUASION_ALC)
					&& expDataMap.get(HOMESTAY_PERSUASION_ALC).equalsIgnoreCase("T")) {
				return false;
			} else {
				return  (MapUtils.isNotEmpty(expDataMap) &&
						expDataMap.containsKey(AB_EXPT_DISABLE_AMENITIES) && Integer.parseInt(expDataMap.get(AB_EXPT_DISABLE_AMENITIES)) != 1);
			}
		}
		return false;
	}

	/*
	 * myPartner change log :
	 *  This is the discount check on the combined mmtDiscount
	 *  The conditions like  - value > 100 and > 5% of the base Price is dynamic and will be controlled from fpm
	 * */
	public boolean enableDiscount(DisplayPriceBreakDown displayPriceBreakDown) {

		return Objects.nonNull(discountParameters) && Objects.nonNull(displayPriceBreakDown) &&
				(
						displayPriceBreakDown.getTotalSaving() >= discountParameters.get(Constants.DISCOUNT_THRESHOLD) &&
								displayPriceBreakDown.getTotalSaving() >= (discountParameters.get(Constants.DISCOUNT_THRESHOLD_PERCENT) * displayPriceBreakDown.getDisplayPrice())
				);
	}

    public boolean enableDiscount(com.gommt.hotels.orchestrator.model.response.listing.PriceDetail priceDetail) {
        if (priceDetail == null) {
            return false;
        }
        double saving = priceDetail.getHotelDiscount() + priceDetail.getCouponDiscount();
        return Objects.nonNull(discountParameters) && (saving >= discountParameters.get(Constants.DISCOUNT_THRESHOLD) && saving >= (discountParameters.get(Constants.DISCOUNT_THRESHOLD_PERCENT) * priceDetail.getDisplayPrice()));
    }

    public List<ReasonForBooking> buildReasonForBooking(List<com.mmt.hotels.model.response.corporate.ReasonForBooking> reasonForBooking) {
        if (CollectionUtils.isNotEmpty(reasonForBooking)) {
            List<com.mmt.hotels.clientgateway.response.corporate.ReasonForBooking> reasonForBookings = new ArrayList<>();
            for (com.mmt.hotels.model.response.corporate.ReasonForBooking reason : reasonForBooking) {
                com.mmt.hotels.clientgateway.response.corporate.ReasonForBooking node = new com.mmt.hotels.clientgateway.response.corporate.ReasonForBooking();
                node.setInputType(reason.getInputType());
                node.setText(reason.getText());
                reasonForBookings.add(node);
            }
            return reasonForBookings;
        }
        return null;
    }

	public List<com.mmt.hotels.clientgateway.response.AttributesFacility> buildChildAttributesCgFromHes(List<com.mmt.model.AttributesFacility> childAttributesHes) {

		List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG = new ArrayList<>();
		for(com.mmt.model.AttributesFacility childAttributeHes : childAttributesHes) {
			com.mmt.hotels.clientgateway.response.AttributesFacility childAttributeCG = new com.mmt.hotels.clientgateway.response.AttributesFacility();
			BeanUtils.copyProperties(childAttributeHes, childAttributeCG);
			childAttributesCG.add(childAttributeCG);
		}
		return  childAttributesCG;
	}

	public List<ReasonForSkipApproval> buildSkipApprovalReasons(List<com.mmt.hotels.model.response.corporate.ReasonForSkipApproval> skipApprovalReasonsHES) {
		if(CollectionUtils.isNotEmpty(skipApprovalReasonsHES)) {
			List<com.mmt.hotels.clientgateway.response.corporate.ReasonForSkipApproval> skipApprovalReasons = new ArrayList<>();
			for(com.mmt.hotels.model.response.corporate.ReasonForSkipApproval reason : skipApprovalReasonsHES) {
				com.mmt.hotels.clientgateway.response.corporate.ReasonForSkipApproval node = new com.mmt.hotels.clientgateway.response.corporate.ReasonForSkipApproval();
				node.setInputType(reason.getInputType());
				node.setText(reason.getText());
				node.setEnablePersonalCorpBooking(reason.getEnablePersonalCorpBooking());
				skipApprovalReasons.add(node);
			}
			return skipApprovalReasons;
		}
		return null;
	}

	public CorpAutobookRequestorConfigBO buildCorpAutobookRequestorConfig(AutobookRequestorConfigBO autobookRequestorConfigHES) {
		if(autobookRequestorConfigHES != null) {
			CorpAutobookRequestorConfigBO corpAutobookRequestorConfig = new CorpAutobookRequestorConfigBO();
			corpAutobookRequestorConfig.setTitle(autobookRequestorConfigHES.getTitle());
			corpAutobookRequestorConfig.setSubTitle(autobookRequestorConfigHES.getSubTitle());
			// TODO: setTravelReasons
			List<Reason> reasons = new ArrayList<>();
			if(autobookRequestorConfigHES.getTravelReasonsConfig() != null) {
				Reason travelReasons = new Reason();
				travelReasons.setTitle(autobookRequestorConfigHES.getTravelReasonsConfig().getTitle());
				travelReasons.setReasonKey(KEY_TRAVEL_REASONS);
				if(CollectionUtils.isNotEmpty(autobookRequestorConfigHES.getTravelReasonsConfig().getOptions())) {
					List<ReasonOption> reasonOptions = new ArrayList<>();
					for(TravelReasonOption travelReasonOption: autobookRequestorConfigHES.getTravelReasonsConfig().getOptions()) {
						ReasonOption reasonOption = new ReasonOption();
						reasonOption.setText(travelReasonOption.getText());
						reasonOption.setInputType(travelReasonOption.getInputType());
						reasonOptions.add(reasonOption);
					}
					travelReasons.setOptions(reasonOptions);
				}

				reasons.add(travelReasons);
			}

			if(autobookRequestorConfigHES.getSkipApprovalReasonsConfig() != null) {
				Reason skipApprovalReasons = new Reason();
				skipApprovalReasons.setTitle(autobookRequestorConfigHES.getSkipApprovalReasonsConfig().getTitle());
				skipApprovalReasons.setReasonKey(KEY_SKIP_APPROVAL_REASONS);
				if(CollectionUtils.isNotEmpty(autobookRequestorConfigHES.getSkipApprovalReasonsConfig().getOptions())) {
					List<ReasonOption> reasonOptions = new ArrayList<>();
					for(SkipApprovalReasonOption skipApprovalReasonOption: autobookRequestorConfigHES.getSkipApprovalReasonsConfig().getOptions()) {
						ReasonOption reasonOption = new ReasonOption();
						reasonOption.setText(skipApprovalReasonOption.getText());
						reasonOption.setInputType(skipApprovalReasonOption.getInputType());
						reasonOption.setEnablePersonalCorpBooking(skipApprovalReasonOption.getEnablePersonalCorpBooking());
						reasonOptions.add(reasonOption);
					}
					skipApprovalReasons.setOptions(reasonOptions);
				}

				reasons.add(skipApprovalReasons);
			}

			if(autobookRequestorConfigHES.getGuestHouseReasonsConfig() != null){
				Reason guestHouseReasons = new Reason();
				guestHouseReasons.setTitle(autobookRequestorConfigHES.getGuestHouseReasonsConfig().getTitle());
				guestHouseReasons.setReasonKey(KEY_GUEST_HOUSE_REASONS);
				if(CollectionUtils.isNotEmpty(autobookRequestorConfigHES.getGuestHouseReasonsConfig().getOptions())) {
					List<ReasonOption> reasonOptions = new ArrayList<>();
					for(GuestHouseReasonOption guestHouseReasonOption: autobookRequestorConfigHES.getGuestHouseReasonsConfig().getOptions()) {
						ReasonOption reasonOption = new ReasonOption();
						reasonOption.setText(guestHouseReasonOption.getText());
						reasonOption.setInputType(guestHouseReasonOption.getInputType());
						reasonOptions.add(reasonOption);
					}
					guestHouseReasons.setOptions(reasonOptions);
				}

				reasons.add(guestHouseReasons);
			}

			corpAutobookRequestorConfig.setTravelReasons(reasons);
			return corpAutobookRequestorConfig;
		}
		return null;
	}

	public GeoLocation buildGeoLocation(HotelResult hotelResult) {
		if (hotelResult == null || hotelResult.getLatitude() == null || hotelResult.getLongitude() == null)
			return null;
		GeoLocation geoLocationCG = new GeoLocation();
		geoLocationCG.setLatitude(hotelResult.getLatitude());
		geoLocationCG.setLongitude(hotelResult.getLongitude());
		return geoLocationCG;
	}


	public com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline buildCancellationPolicyTimeline(com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline, boolean enableThemification, BNPLVariant bnplVariant) {
		if (cancellationTimeline != null && CollectionUtils.isNotEmpty(cancellationTimeline.getCancellationPolicyTimelineList())) {
			com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline cancellationPolicyTimeline = new com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline();
			if (enableThemification) {
				cancellationPolicyTimeline.setTimelinesV2(buildCancellationTimelineV2List(cancellationTimeline.getCancellationPolicyTimelineList()));
			} else {
				cancellationPolicyTimeline.setTimeline(buildCancellationTimelineList(cancellationTimeline.getCancellationPolicyTimelineList()));
			}
			cancellationPolicyTimeline.setCancellationDate(cancellationTimeline.getCancellationDate());
			cancellationPolicyTimeline.setCancellationDateTime(cancellationTimeline.getCancellationDateTime());

			// Below check is placed to ensure card charge date is not overlapped due to partial refundable plans
			if (CollectionUtils.isNotEmpty(cancellationPolicyTimeline.getTimeline()) && cancellationPolicyTimeline.getTimeline().size() < 3) {
				cancellationPolicyTimeline.setCardChargeDate(cancellationTimeline.getCardChargeDate());
				cancellationPolicyTimeline.setCardChargeDateTime(cancellationTimeline.getCardChargeDateTime());
				cancellationPolicyTimeline.setCardChargeText(cancellationTimeline.getCardChargeText());
			}

			cancellationPolicyTimeline.setDateFormat(cancellationTimeline.getDateFormat());
			cancellationPolicyTimeline.setBookingAmountText(cancellationTimeline.getBookingAmountText());
			cancellationPolicyTimeline.setFreeCancellationBenefits(buildFreeCancellationBenefits(cancellationTimeline.getFreeCancellationBenefits(), bnplVariant));
			cancellationPolicyTimeline.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.RATEPLAN_CANCELLATION_POLICY));
			if (enableThemification) {
				cancellationPolicyTimeline.setHeaderLeft(polyglotService.getTranslatedData(RATEPLAN_CANCELLATION_POLICY));
				cancellationPolicyTimeline.setHeaderRight(polyglotService.getTranslatedData(APPLICABLE_REFUND_TEXT));
			}
			return cancellationPolicyTimeline;
		}
		return null;
	}

	private List<com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline> buildCancellationTimelineList(List<CancellationPolicyTimeline> cancellationPolicyTimelineList) {
		if (CollectionUtils.isNotEmpty(cancellationPolicyTimelineList)) {
			List<com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline> cancellationTimelineList = new ArrayList<>();
			cancellationPolicyTimelineList.forEach(cancellationPolicyTimelineHES -> cancellationTimelineList.add(buildTimeline(cancellationPolicyTimelineHES)));
			return cancellationTimelineList;
		}
		return null;
	}

	private List<CancellationTimelineV2> buildCancellationTimelineV2List(List<CancellationPolicyTimeline> cancellationPolicyTimelineList) {
		if (CollectionUtils.isNotEmpty(cancellationPolicyTimelineList)) {
			List<CancellationTimelineV2> cancellationTimelineList = new ArrayList<>();
			cancellationPolicyTimelineList.forEach(cancellationPolicyTimelineHES -> cancellationTimelineList.add(buildTimelineV2(cancellationPolicyTimelineHES)));
			return cancellationTimelineList;
		}
		return null;
	}

	private CancellationTimelineV2 buildTimelineV2(CancellationPolicyTimeline cancellationPolicyTimelineHES) {
		if (cancellationPolicyTimelineHES != null) {
			CancellationTimelineV2 cancellationPolicyTimeline = new CancellationTimelineV2();
			cancellationPolicyTimeline.setStartDateText(cancellationPolicyTimelineHES.getStartDate());
			cancellationPolicyTimeline.setStartDateSubText(cancellationPolicyTimelineHES.getStartDateTime());
			cancellationPolicyTimeline.setEndDateText(cancellationPolicyTimelineHES.getEndDate());
			cancellationPolicyTimeline.setEndDateSubText(cancellationPolicyTimelineHES.getEndDateTime());
			cancellationPolicyTimeline.setType(cancellationPolicyTimelineHES.getType());
			cancellationPolicyTimeline.setRefundText(cancellationPolicyTimelineHES.getRefundText());
			if (cancellationPolicyTimelineHES.getFlexiCancellationDetails() != null) {
				com.mmt.hotels.clientgateway.response.rooms.FlexiCancellationDetails flexiCancellationDetails = new com.mmt.hotels.clientgateway.response.rooms.FlexiCancellationDetails();
				BeanUtils.copyProperties(cancellationPolicyTimelineHES.getFlexiCancellationDetails(), flexiCancellationDetails);
				cancellationPolicyTimeline.setFlexiCancellationDetails(flexiCancellationDetails);
			}
			return cancellationPolicyTimeline;
		}
		return null;
	}

	private com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline buildTimeline(CancellationPolicyTimeline cancellationPolicyTimelineHES) {
		if (cancellationPolicyTimelineHES != null) {
			com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline cancellationPolicyTimeline = new  com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline();
			cancellationPolicyTimeline.setRefundable(cancellationPolicyTimelineHES.isRefundable());
			cancellationPolicyTimeline.setText(cancellationPolicyTimelineHES.getText());
			cancellationPolicyTimeline.setStartDate(cancellationPolicyTimelineHES.getStartDate());
			cancellationPolicyTimeline.setEndDate(cancellationPolicyTimelineHES.getEndDate());
			cancellationPolicyTimeline.setEndDateTime(cancellationPolicyTimelineHES.getEndDateTime());
			if (cancellationPolicyTimelineHES.getFlexiCancellationDetails() != null) {
				com.mmt.hotels.clientgateway.response.rooms.FlexiCancellationDetails flexiCancellationDetails = new com.mmt.hotels.clientgateway.response.rooms.FlexiCancellationDetails();
				BeanUtils.copyProperties(cancellationPolicyTimelineHES.getFlexiCancellationDetails(), flexiCancellationDetails);
				cancellationPolicyTimeline.setFlexiCancellationDetails(flexiCancellationDetails);
			}
			return cancellationPolicyTimeline;
		}
		return null;
	}

	public com.mmt.hotels.clientgateway.response.rooms.PaymentPlan buildPaymentPlan(com.mmt.hotels.model.response.pricing.PaymentPlan paymentPlan) {
		if(paymentPlan != null && paymentPlan.getAmount() != 0.0) {
			com.mmt.hotels.clientgateway.response.rooms.PaymentPlan paymentPlanCG = new com.mmt.hotels.clientgateway.response.rooms.PaymentPlan();
			paymentPlanCG.setText(paymentPlan.getText());
			paymentPlanCG.setAmount(paymentPlan.getAmount());
			if(CollectionUtils.isNotEmpty(paymentPlan.getPaymentPolicy())) {
				AtomicInteger index = new AtomicInteger(1);
				paymentPlanCG.setPaymentPolicy(new ArrayList<>());
				paymentPlan.getPaymentPolicy().forEach(policy-> {
					com.mmt.hotels.clientgateway.response.rooms.PaymentPlan policyCG = new com.mmt.hotels.clientgateway.response.rooms.PaymentPlan();
					policyCG.setSequence(index.getAndIncrement());
					policyCG.setText(policy.getText());
					policyCG.setAmount(policy.getAmount());
					policyCG.setPaymentDate(buildPaymentDate(policy.getPaymentDateText())); // HTL-37096
					paymentPlanCG.getPaymentPolicy().add(policyCG);
				});
				paymentPlanCG.setPenaltyText(polyglotService.getTranslatedData(PAYMENT_PLAN_PENALTY_TEXT));
			}
			return paymentPlanCG;
		}
		return null;
	}

	private String buildPaymentDate(String paymentDateHES){
		if(paymentDateHES != null && paymentDateHES.split(COMMA).length > 1){
			return paymentDateHES.split(COMMA)[0];
		}
		return null;
	}

	public MyBizQuickPayConfigBO buildMyBizQuickPayConfig(DisplayPriceBreakDown displayPriceBrkDwn, String currency) {
		MyBizQuickPayConfigBO myBizQuickPayConfigBO = new MyBizQuickPayConfigBO();
		myBizQuickPayConfigBO.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_QUICKPAY_TITLE));
		myBizQuickPayConfigBO.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_QUICKPAY_SUBTITLE));
		myBizQuickPayConfigBO.setText(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_QUICKPAY_TEXT_CURRENCY));
		myBizQuickPayConfigBO.setCtaSubText(Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) ?
				polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_QUICKPAY_CTA_DESKTOP) : polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_QUICKPAY_CTA_DESKTOP));

		myBizQuickPayConfigBO.setText(populateMyBizQuickPayTextWithPricingInfo(myBizQuickPayConfigBO.getText(), displayPriceBrkDwn, currency));
		return myBizQuickPayConfigBO;

	}

	private String populateMyBizQuickPayTextWithPricingInfo(String text, DisplayPriceBreakDown displayPriceBrkDwn, String askedCurrency) {
		if (displayPriceBrkDwn != null) {
			double totalDiscountAmount = displayPriceBrkDwn.getMmtDiscount() + displayPriceBrkDwn.getBlackDiscount() + displayPriceBrkDwn.getCdfDiscount() + displayPriceBrkDwn.getWallet();
			double priceAfterDiscountAmount = displayPriceBrkDwn.getBasePrice();
			if (totalDiscountAmount > 0.0d) {
				priceAfterDiscountAmount = priceAfterDiscountAmount - totalDiscountAmount;
			}
			double totalPrice = displayPriceBrkDwn.getDisplayPrice();
			double hotelTaxes = totalPrice - priceAfterDiscountAmount;
			// Get currency symbol for user selected currency.
			String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : DEFAULT_CUR_INR).getCurrencySymbol();
			text = StringUtils.replace(text, "{CURRENCY_SYMBOL}", currencySymbol);
			text = StringUtils.replace(text, "{TOTAL_AMOUNT}", "" + totalPrice);
			text = StringUtils.replace(text, "{PRICE_AFTER_DISCOUNT}", "" + priceAfterDiscountAmount);
			text = StringUtils.replace(text, "{HOTEL_TAXES}", "" + hotelTaxes);

			return text;
		}
		return null;

	}

	/*
	 * Steps to find the drivingTimeText for a hotel for ShortStay funnel case:
	 * Iterate over the drivingDurationBucketMap(where Key:time range in minutes,
	 * Value:Corresponding Driving text) and return the corresponding drivingTimeText
	 * if drivingDuration(in min) lies in the time range.
	 */
	//TO DO : Move this method to Util file
	public String buildDrivingTimeText(Double drivingTimeInMins) {
		if(drivingTimeInMins!=null) {
			for (Map.Entry<String,String> entry : listingDrivingDurationBucketMap.entrySet()) {
				List<String> timeRange = Arrays.asList(entry.getKey().split(Constants.HYPEN));
				if(timeRange.size()==1) {
					int minTimeRange = Integer.parseInt(timeRange.get(0));
					if(drivingTimeInMins>=minTimeRange) {
						return polyglotService.getTranslatedData(entry.getValue());
					}
				} else {
					int minTimeRange = Integer.parseInt(timeRange.get(0));
					int maxTimeRange = Integer.parseInt(timeRange.get(1));
					if(drivingTimeInMins>=minTimeRange && drivingTimeInMins<=maxTimeRange) {
						return polyglotService.getTranslatedData(entry.getValue());
					}
				}
			}

		}
		return null;
	}


	//ShortStay Funnel is only for ANDROID and IOS.
	//TO DO : Move this method to proper file
	public boolean isEligibleForNearbyFlow(DeviceDetails deviceDetails) {
		if (deviceDetails != null && ((Constants.ANDROID).equalsIgnoreCase(deviceDetails.getBookingDevice()) || (Constants.DEVICE_IOS).equalsIgnoreCase(deviceDetails.getBookingDevice()))) {
			return true;
		}
		return false;
	}

	public void updateCancellationPolicyFCSubtext(BookedCancellationPolicy cancellationPolicy, Boolean isPeakDate, String policyDateTime, boolean enableThemification, String pricerBnplMessage) {
		if (cancellationPolicy != null && BookedCancellationPolicyType.FC.equals(cancellationPolicy.getType())) {
			String subtextSuffix = "";
			if (isPeakDate != null && isPeakDate) {
				subtextSuffix = polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_BNPL_PEAK_SUBTEXT);
			} else {
				subtextSuffix = polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_BNPL_NONPEAK_SUBTEXT);
			}
			if(Utility.isGccOrKsa()) {
				subtextSuffix = subtextSuffix.replace("<zone>", GULF_STANDARD_TIME);
			} else {
				subtextSuffix = subtextSuffix.replace("<zone>", IST);
			}
			if (StringUtils.isNotEmpty(subtextSuffix) && StringUtils.isNotEmpty(policyDateTime)) {
				subtextSuffix = subtextSuffix.replace("<DateTime>", policyDateTime);
				if(StringUtils.isNotEmpty(pricerBnplMessage) && StringUtils.isNotEmpty(polyglotService.getTranslatedData(pricerBnplMessage))) {
					subtextSuffix = subtextSuffix +" "+ polyglotService.getTranslatedData(pricerBnplMessage);
				}
				String policySubtext = cancellationPolicy.getSubText();
				if (StringUtils.isNotEmpty(policySubtext)) {
					if (enableThemification) {
						cancellationPolicy.setSubText(policySubtext + subtextSuffix);
					} else {
						policySubtext = policySubtext + FULLSTOP_SPACE + subtextSuffix;
						cancellationPolicy.setSubText(policySubtext);
					}
				}
			}
		}
	}


	/**
	 *
	 * Text like 2 nights, 6 rooms will be created on the basis of roomCount and Night count
	 *
	 * @param roomCount  number of rooms
	 * @param nightCount  number of nights
	 */
	public String getGroupPriceTextForGRPNT(Integer roomCount, Integer nightCount) {
		if (roomCount == null || nightCount == null) {
			return null;
		}

		if(nightCount == 1 && roomCount == 1) {
			return polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM_NEW_DESIGN)
					.replace(NIGHT_COUNT, String.valueOf(nightCount))
					.replace(ROOM_COUNT, String.valueOf(roomCount));

		} else if(nightCount == 1) {
			return polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_NIGHT_NEW_DESIGN)
					.replace(NIGHT_COUNT, String.valueOf(nightCount))
					.replace(ROOM_COUNT, String.valueOf(roomCount));
		} else if(roomCount == 1) {
			return polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_ROOM_NEW_DESIGN)
					.replace(NIGHT_COUNT, String.valueOf(nightCount))
					.replace(ROOM_COUNT, String.valueOf(roomCount));
		} else {
			return polyglotService.getTranslatedData(GROUP_PRICE_TEXT_NEW_DESIGN)
					.replace(NIGHT_COUNT, String.valueOf(nightCount))
					.replace(ROOM_COUNT, String.valueOf(roomCount));
		}
	}

	public String getGroupPriceTextForGRPNTForAltAcco(Integer nightCount) {

		if(nightCount == null)
			return StringUtils.EMPTY;

		if(nightCount == 1) {
			return polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_NIGHT_ALT_ACCO)
					.replace(NIGHT_COUNT, String.valueOf(nightCount));

		} else {
			return polyglotService.getTranslatedData(GROUP_PRICE_TEXT_MULTI_NIGHT_ALT_ACCO)
					.replace(NIGHT_COUNT, String.valueOf(nightCount));
		}
	}
	public Map<String, Object> fetchNonBnplAppliedCouponDetails(TotalPricing totalPricing){
		boolean bnplDisabledDueToNonBnplCouponApplied = false;
		String nonBnplCouponAppliedCode = "current";
		if(totalPricing != null && totalPricing.getCoupons() != null) {
			for (Coupon coupon : totalPricing.getCoupons()) {
				if (coupon.isAutoApplicable() && !coupon.isBnplAllowed()) {
					bnplDisabledDueToNonBnplCouponApplied = true;
					nonBnplCouponAppliedCode = coupon.getCode();
					break;
				}
			}
		}
		Map<String, Object> nonBnplAppliedCouponDetailsMap = new HashMap<>();
		nonBnplAppliedCouponDetailsMap.put(Constants.BNPL_DISABLED_DUE_TO_NON_BNPL_COUPON_APPLIED, bnplDisabledDueToNonBnplCouponApplied);
		nonBnplAppliedCouponDetailsMap.put(Constants.NON_BNPL_COUPON_APPLIED_CODE, nonBnplCouponAppliedCode);
		return nonBnplAppliedCouponDetailsMap;
	}
	public BNPLDisabledReason getBNPLDisabledReason(boolean userLevelBnplDisabled, boolean bnplDisabledDueToNonBnplCouponApplied, boolean insuranceAddonSelected) {
		if (userLevelBnplDisabled) {
			return BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD;
		} else if (bnplDisabledDueToNonBnplCouponApplied) {
			return BNPLDisabledReason.NON_BNPL_COUPON_APPLIED;
		} else if (insuranceAddonSelected) {
			return BNPLDisabledReason.INSURANCE_APPLIED;
		}
		return null;
	}

	public PriceFooter buildPriceFooter(boolean taxIncluded, int los, int roomCount) {
		PriceFooter priceFooter = new PriceFooter();
		priceFooter.setText(polyglotService.getTranslatedData(INCLUSIVE_OF_TAXES_AND_FEES));
		//priceFooter.setSubtext(MessageFormat.format(Constants.PRICE_FOOTER_SUBTEXT, los, los > 1 ? PLURAL_STRING : EMPTY_STRING, roomCount, roomCount > 1 ? PLURAL_STRING : EMPTY_STRING));
		return priceFooter;
	}

	public PriceFooter buildBNPLPriceFooter() {
		PriceFooter priceFooter = new PriceFooter();
		priceFooter.setCtaText(Utility.isGccOrKsa() ? polyglotService.getTranslatedData(PRICE_FOOTER_CTA_TEXT_GCC) : polyglotService.getTranslatedData(PRICE_FOOTER_CTA_TEXT));
		priceFooter.setAmountText(Utility.isGccOrKsa() ? polyglotService.getTranslatedData(PRICE_FOOTER_AMOUNT_TEXT_GCC) : polyglotService.getTranslatedData(PRICE_FOOTER_AMOUNT_TEXT));
		priceFooter.setText(polyglotService.getTranslatedData(PRICE_FOOTER_TEXT));
		priceFooter.setSubtext(StringUtils.isNotEmpty(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_FOOTER_SUBTEXT)) ? polyglotService.getTranslatedData(ConstantsTranslation.PRICE_FOOTER_SUBTEXT) : EMPTY_STRING);
		return priceFooter;
	}

	public AddOnInfo getAddonInfo(boolean taxIncluded, Double foodRating, String siteDomain, Map<String, String> expDataMap, List<UpsellOptions> upsellOptions, AddOnState selectedAddOnState) {
		AddOnInfo addonInfo = new AddOnInfo();
		boolean showFoodRating = true;
		if (!utility.isExperimentOn(expDataMap, EXP_ADD_ON_PERSUASION) && selectedAddOnState != null) {
			showFoodRating = false;
		}
		addonInfo.setTitle(polyglotService.getTranslatedData(ADD_ONS));
		addonInfo.setSubtitle(polyglotService.getTranslatedData(INCLUSIVE_OF_TAXES));
		HotelTag hotelTag = new HotelTag();
		if(Utility.isRegionGccOrKsa(siteDomain) && MapUtils.isNotEmpty(expDataMap) && utility.isExperimentOn(expDataMap, EXP_GBRP)){
			hotelTag.setTitle(addOnInfoMostPopularTag.get(TITLE));
			hotelTag.setColor(addOnInfoMostPopularTag.get(COLOR));
		}
		else {
			hotelTag.setTitle(addOnInfoTag.get(TITLE));
			hotelTag.setColor(addOnInfoTag.get(COLOR));
		}
		addonInfo.setTag(hotelTag);
		if (foodRating != null && showFoodRating) {
			int percent = (int) (foodRating * 20);
			if (percent >= foodRatingThresold) {
				String text = polyglotService.getTranslatedData(ConstantsTranslation.MEAL_UPSELL_DESC_TEXT);
				if (StringUtils.isNotEmpty(text)) {
					addonInfo.setDescriptionText(text.replace("{percent}", percent + "%"));
				}
			}
		}
		return addonInfo;
	}

	public void updateHotelCategories(boolean enableThemification, List<String> categories, Set<String> hotelCategories) {
		if (enableThemification && MapUtils.isNotEmpty(categoryTextToCategoryTypeMap) && CollectionUtils.isNotEmpty(categories) && CollectionUtils.isNotEmpty(hotelCategories)) {
			Set<String> categoriesSet = categories.stream().collect(Collectors.toSet());
			hotelCategories.forEach(category -> {
				if (StringUtils.isNotEmpty(categoryTextToCategoryTypeMap.get(category)) && !categoriesSet.contains(category)) {
					categories.add(categoryTextToCategoryTypeMap.get(category));
				}
			});
		}
	}
	public void updatePriceFooterForPAHOnly(String payMode, TotalPricing totalpricing) {
		if (Utility.isPahOnlyPaymode(payMode)) {
			PriceFooter priceFooter = new PriceFooter();
			priceFooter.setText(polyglotService.getTranslatedData(PRICE_FOOTER_TEXT));
			totalpricing.setPriceFooter(priceFooter);
		}
	}

	/**
	 * Method to create scarcity tags for Review Page. Logic to show the tag (config: scarcityReviewThreshold):
	 * availableCount < totalRoomCount + scarcityReviewThreshold
	 * For Desktop scarcity texts will be sent using Alerts.
	 * For othe clients it will sent in Hotel Tags.
	 * @param hotelRates
	 * @param availRoomsResponse
	 */
	public void buildScarcityHotelTag(HotelRates hotelRates, AvailRoomsResponse availRoomsResponse) {
		int availableCount = getAvailableCount(hotelRates);
		int totalRoomCount = hotelRates != null && hotelRates.getRoomTypeDetails() != null && hotelRates.getRoomTypeDetails().getTotalDisplayFare() != null ? hotelRates.getRoomTypeDetails().getTotalDisplayFare().getTotalRoomCount() : Integer.MAX_VALUE;
		if (availableCount != Integer.MAX_VALUE && totalRoomCount != Integer.MAX_VALUE && (availableCount < totalRoomCount + scarcityReviewThreshold)) {
			String tagTitle = polyglotService.getTranslatedData(REVIEW_SCARCITY_TITLE)
					.replace(PLACEHOLDER_AVAILABLE_COUNT, String.valueOf(availableCount))
					.replace(PLACEHOLDER_PLURAL, availableCount > 1 ? PLURAL_STRING : EMPTY_STRING);
			if (Utility.isAppRequest() || Utility.isPWARequest()) {
				Map<String, HotelTag> hotelTagMap = availRoomsResponse.getHotelInfo().getHotelTags();
				if (MapUtils.isEmpty(hotelTagMap)) {
					hotelTagMap = new HashMap<>();
				}
				HotelTag hotelTag = new HotelTag();
				hotelTag.setTitle(tagTitle);
				hotelTag.setBackground(scarcityBackground);
				hotelTag.setType(HotelTagType.SCARCITY.getValue());
				hotelTagMap.put(PLACEHOLDER_PC_HOTEL_TOP, hotelTag);
				availRoomsResponse.getHotelInfo().setHotelTags(hotelTagMap);
			} else {
				if (CollectionUtils.isEmpty(availRoomsResponse.getAlerts())) {
					availRoomsResponse.setAlerts(new ArrayList<>());
				}
				Alert alert = new Alert();
				alert.setText(tagTitle);
				alert.setType(AlertType.PRICE_INCREASE);
				availRoomsResponse.getAlerts().add(alert);
			}
		}
	}

	public void buildHighDemandHotelTag(AvailRoomsResponse availRoomsResponse){
		if(availRoomsResponse != null && availRoomsResponse.getHotelInfo() != null){
			Map<String, HotelTag> hotelTagMap = MapUtils.isNotEmpty(availRoomsResponse.getHotelInfo().getHotelTags()) ?
					availRoomsResponse.getHotelInfo().getHotelTags() : new HashMap<>();
			HotelTag hotelTag = new HotelTag();
			hotelTag.setTitle(polyglotService.getTranslatedData(HIGH_DEMAND_PERSUASION));
			hotelTag.setBackground(highDemandBackgroundColor);
			hotelTag.setTitleColor(highDemandTitleColor);
			hotelTagMap.put(PLACEHOLDER_PC_HOTEL_TOP, hotelTag);
			availRoomsResponse.getHotelInfo().setHotelTags(hotelTagMap);
		}
	}

	private int getAvailableCount(HotelRates hotelRates) {
		AtomicInteger availableCount = new AtomicInteger(Integer.MAX_VALUE);
		if (hotelRates != null && hotelRates.getRoomTypeDetails() != null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
			hotelRates.getRoomTypeDetails().getRoomType().values().forEach(roomType -> {
						if (roomType != null && MapUtils.isNotEmpty(roomType.getRatePlanList())) {
							roomType.getRatePlanList().values().forEach(ratePlan -> availableCount.set(Math.min(ratePlan.getAvailDetails().getCount(), availableCount.get())));
						}
					}
			);
		}
		return availableCount.get();
	}

	public void buildTopSectionPersuasion(Hotel hotel, String tag, String sectionName, String deviceName) {

		String tagValue = polyglotService.getTranslatedData(tag);

		if(CollectionUtils.isNotEmpty(pcTopSectionPersuasionAllowedSections) && pcTopSectionPersuasionAllowedSections.contains(sectionName) &&
				StringUtils.isNotEmpty(tag) && StringUtils.isNotEmpty(tagValue)) {

			try {
				Map<Object,Object> hotelPersuasions = (Map<Object,Object>) hotel.getHotelPersuasions();
				Map<String,String> personalizedPicksIconUrlTagMap = gson.fromJson(personalizedPicksIconUrlTag, new TypeToken<Map<String,String>>() {
				}.getType());
				String iconUrl = personalizedPicksIconUrlTagMap.get(tag);

				if (CLIENT_DESKTOP.equalsIgnoreCase(deviceName) || DEVICE_OS_PWA.equalsIgnoreCase(deviceName)) {
					Persuasion pcTopSectionPersuasion = gson.fromJson(pcTopSectionPersuasionDesktopConfig, new TypeToken<Persuasion>() {
					}.getType());
					Map<String,String> personalizedPicksStyleClassTagDesktopMap = gson.fromJson(personalizedPicksStyleClassTagDesktop, new TypeToken<Map<String,String>>() {
					}.getType());
					pcTopSectionPersuasion.getData().get(0).getStyle().setStyleClasses(Arrays.asList(personalizedPicksStyleClassTagDesktopMap.get(tag)));
					pcTopSectionPersuasion.getData().get(0).setText(tagValue);
					pcTopSectionPersuasion.getData().get(0).setIconurl(iconUrl);
					hotelPersuasions.put("PC_TOP_SECTION", pcTopSectionPersuasion);
				} else {
					Persuasion pcTopSectionPersuasion = gson.fromJson(pcTopSectionPersuasionAppsConfig, new TypeToken<Persuasion>() {
					}.getType());
					Map<String,String> personalizedPicksColorTagAppsMap = gson.fromJson(personalizedPicksColorTagApps, new TypeToken<Map<String,String>>() {
					}.getType());
					pcTopSectionPersuasion.getData().get(0).setText(tagValue);
					pcTopSectionPersuasion.getData().get(0).getStyle().setTextColor(personalizedPicksColorTagAppsMap.get(tag));
					pcTopSectionPersuasion.getData().get(0).setIconurl(iconUrl);
					hotelPersuasions.put("PLACEHOLDER_CARD_M6", pcTopSectionPersuasion);
				}
			} catch (Exception ex) {
				logger.error("Error while building section tage persuasion ",ex);
			}
		}
	}

	public RateplansUpgrade prepareUpgradeInfo(HotelRates hotelRates, TotalPricing totalPricing, int los, BlackBenefits blackBenefits) {
		UpgradeInfo upgradeInfo = hotelRates.getUpgradeInfo() != null ? hotelRates.getUpgradeInfo() : new UpgradeInfo();
		RoomInfo baseRoomInfo = getBaseRoomInfo(hotelRates.getRoomTypeDetails(), hotelRates.getRoomInfo());
		RoomInfo upgradedRoomInfo = upgradeInfo.getRoomInfo();
		com.mmt.hotels.model.response.mmtprime.BlackInfo blackInfo = hotelRates.getBlackInfo() != null ? hotelRates.getBlackInfo() : new com.mmt.hotels.model.response.mmtprime.BlackInfo();
		RateplansUpgrade rateplansUpgrade = new RateplansUpgrade();
		rateplansUpgrade.setTierImage(upgradeInfo.getTierImage());
		rateplansUpgrade.setBlackPopupIcon(upgradeInfo.getBlackPopupIcon());
		rateplansUpgrade.setTitle(getTitle(blackBenefits));
		rateplansUpgrade.setDisclaimer(polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_POPUP_DISCLAIMER));
		rateplansUpgrade.setSelectedTitle(polyglotService.getTranslatedData(SELECTED_TITLE));
		rateplansUpgrade.setUpgradedTitle(getUpgradedTitle(blackBenefits));
		rateplansUpgrade.setSelectedRateplans(buildRatePlanDetails(totalPricing, upgradedRoomInfo, los, null, hotelRates.getCurrencyCode()));
		rateplansUpgrade.setUpgradedRateplans(buildRatePlanDetails(totalPricing, baseRoomInfo, los, blackInfo.getTierName(), hotelRates.getCurrencyCode()));
		rateplansUpgrade.setUpgradeType(utility.getUpgradeType(blackBenefits));
		//Setting the 1st inclusion in upgraded rate plan
		if(CollectionUtils.isNotEmpty(rateplansUpgrade.getUpgradedRateplans()) && CollectionUtils.isNotEmpty(upgradeInfo.getInclusions())) {
			rateplansUpgrade.getUpgradedRateplans().get(0).setInclusionsList(new ArrayList<>());
			rateplansUpgrade.getUpgradedRateplans().get(0).getInclusionsList().add(utility.prepareBookedInclusionFromInclusion(upgradeInfo.getInclusions().get(0)));
		}
		if (rateplansUpgrade.getUpgradedRateplans() != null && hotelRates.getUpgradeInfo() != null && hotelRates.getUpgradeInfo().getUpgradeRateplanOriginalPriceDetails() != null) {
			updateOriginalPriceTextInUpgradedRateplan(rateplansUpgrade.getUpgradedRateplans(), hotelRates.getUpgradeInfo().getUpgradeRateplanOriginalPriceDetails(), los, hotelRates.getCurrencyCode());
		}
		return rateplansUpgrade;
	}

	private void updateOriginalPriceTextInUpgradedRateplan(List<RatePlanDetails> upgradedRateplans,
														   UpgradedRateplanOriginalPriceDetails upgradeRateplanOriginalPriceDetails, int los, String currency) {
		int price = (int) (upgradeRateplanOriginalPriceDetails.getDisplayPrice() + upgradeRateplanOriginalPriceDetails.getTaxes());
		if (price > 0 && CollectionUtils.isNotEmpty(upgradedRateplans) && upgradedRateplans.get(0) != null && upgradedRateplans.get(0).getPriceMap() != null) {
			price = price / los;
			upgradedRateplans.get(0).getPriceMap().setOriginalPriceMsg(getOriginalPriceText(price, currency));
		}
	}

	private String getOriginalPriceText(int price, String askedCurrency) {
		String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : "INR").getCurrencySymbol();
		String htmlTextForOriginalPrice = polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_ORIGINAL_PRICE);
		if (StringUtils.isNotEmpty(htmlTextForOriginalPrice)) {
			htmlTextForOriginalPrice = htmlTextForOriginalPrice.replace("{currency}", currencySymbol);
			htmlTextForOriginalPrice = htmlTextForOriginalPrice.replace("{totalPrice}", utility.convertNumericValueToCommaSeparatedString(price, Locale.ENGLISH));
		}
		return htmlTextForOriginalPrice;
	}

	private String getTitle(BlackBenefits blackBenefits) {
		// as in GCC, MMT Black is called MMT Select
		if(Utility.isGCC()){
			return getGccTitle(blackBenefits);
		} else {
			return getNonGCCTitle(blackBenefits);
		}
	}

	private String getGccTitle(BlackBenefits blackBenefits) {
		if (blackBenefits.isMealUpgrade() && blackBenefits.isRoomUpgrade()) {
			return polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_POPUP_TITLE_BOTH_GCC);
		} else if (blackBenefits.isMealUpgrade()) {
			return polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_POPUP_TITLE_MEAL_GCC);
		} else if (blackBenefits.isRoomUpgrade()) {
			return polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_POPUP_TITLE_GCC);
		}
		return polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_POPUP_TITLE_GCC);
	}

	private String getNonGCCTitle(BlackBenefits blackBenefits) {
		if (blackBenefits.isMealUpgrade() && blackBenefits.isRoomUpgrade()) {
			return polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_POPUP_TITLE_BOTH);
		} else if (blackBenefits.isMealUpgrade()) {
			return polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_POPUP_TITLE_MEAL);
		} else if (blackBenefits.isRoomUpgrade()) {
			return polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_POPUP_TITLE);
		}
		return polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_POPUP_TITLE);
	}
	private String getUpgradedTitle(BlackBenefits blackBenefits) {
		if (blackBenefits.isMealUpgrade() && blackBenefits.isRoomUpgrade()) {
			return polyglotService.getTranslatedData(UPGRADED_TITLE_MEAL_AND_ROOM);
		} else if (blackBenefits.isMealUpgrade()) {
			return polyglotService.getTranslatedData(UPGRADED_TITLE_MEAL);
		} else if (blackBenefits.isRoomUpgrade()) {
			return polyglotService.getTranslatedData(UPGRADED_TITLE_ROOM);
		}
		return polyglotService.getTranslatedData(UPGRADED_TITLE);
	}

	private RoomInfo getBaseRoomInfo(RoomTypeDetails roomTypeDetails, Map<String, RoomInfo> roomInfo) {
		String baseRoomCode = roomTypeDetails != null && MapUtils.isNotEmpty(roomTypeDetails.getRoomType()) ? roomTypeDetails.getRoomType().values().stream().findFirst().orElse(new RoomType()).getRoomTypeCode() : "";
		return MapUtils.isNotEmpty(roomInfo) ? roomInfo.values().stream().filter(room -> baseRoomCode.equalsIgnoreCase(room.getRoomCode())).findFirst().orElse(new RoomInfo()) : new RoomInfo();
	}

	private List<RatePlanDetails> buildRatePlanDetails(TotalPricing totalPricing, RoomInfo baseRoomInfo, int los, String tierName, String currency) {
		List<RatePlanDetails> ratePlanDetailsList = new ArrayList<>();
		if (baseRoomInfo != null) {
			RatePlanDetails ratePlanDetails = new RatePlanDetails();
			ratePlanDetails.setRoomName(baseRoomInfo.getRoomName());
			ratePlanDetails.setRoomDesc(buildRoomDesc(baseRoomInfo));
			ratePlanDetails.setPriceMap(buildPriceMap(totalPricing, los, currency));
			ratePlanDetails.setBgGradient(bgGradientBlackPopupMap.get(tierName));
			ratePlanDetailsList.add(ratePlanDetails);
		}
		return ratePlanDetailsList;
	}

	private TotalPricing buildPriceMap(TotalPricing totalPricingInput, int los, String currency) {
		TotalPricing totalPricing = new TotalPricing();
		if (totalPricingInput != null) {
			totalPricing.setDetails(preparePriceDetailsPerNight(totalPricingInput.getDetails(), totalPricingInput.getCoupons(), los));
			totalPricing.setNoCouponText(totalPricingInput.getNoCouponText());
			totalPricing.setCouponSubtext(totalPricingInput.getCouponSubtext());
			totalPricing.setPriceDisplayMsg(polyglotService.getTranslatedData(PER_NIGHT_TEXT));
			totalPricing.setPriceTaxMsg(getTaxMessage(totalPricing.getDetails(), StringUtils.isNotBlank(totalPricingInput.getCurrency()) ? totalPricingInput.getCurrency() : DEFAULT_CUR_INR));
			totalPricing.setCouponAmount(totalPricingInput.getCouponAmount());
			totalPricing.setGroupPriceText(totalPricingInput.getGroupPriceText());
			totalPricing.setSavingsText(totalPricingInput.getSavingsText());
			totalPricing.setPinCodeMandatory(totalPricingInput.isPinCodeMandatory());
			totalPricing.setCurrency(currency);
		}
		return totalPricing;
	}

	private String getTaxMessage(List<PricingDetails> details, String currency) {
		Double taxAmount = 0.0;
		for (PricingDetails pricingDetails : details) {
			taxAmount = taxAmount + (Constants.TAXES_KEY.equalsIgnoreCase(pricingDetails.getKey()) ? pricingDetails.getAmount() : 0.0);
		}
		return PLUS + SPACE + Currency.getCurrencyEnum(currency).getCurrencySymbol() + SPACE + taxAmount.intValue() + SPACE + polyglotService.getTranslatedData(ConstantsTranslation.TAXES_LABEL);
	}

	private List<PricingDetails> preparePriceDetailsPerNight(List<PricingDetails> details, List<Coupon> coupons, int los) {
		double preAppliedCouponAmount = CollectionUtils.isNotEmpty(coupons) ? coupons.stream().filter(Coupon::isAutoApplicable).findFirst().map(Coupon::getCouponAmount).orElse(0.0) : 0.0;
		List<PricingDetails> pricingDetails = new ArrayList<>();
		boolean discountedPriceExist = false;
		if (CollectionUtils.isNotEmpty(details)) {
			for (PricingDetails pricingDetail : details) {
				PricingDetails pricingDetailPerNight = new PricingDetails();
				pricingDetailPerNight.setAmount(getAmount(pricingDetail.getKey(), pricingDetail.getAmount(), los, preAppliedCouponAmount));
				pricingDetailPerNight.setLabel(pricingDetail.getLabel());
				pricingDetailPerNight.setType(pricingDetail.getType());
				pricingDetailPerNight.setKey(pricingDetail.getKey());
				pricingDetailPerNight.setHotelierCurrencyAmount(pricingDetail.getHotelierCurrencyAmount() != null && los > 0 ? pricingDetail.getHotelierCurrencyAmount() / los : null);
				pricingDetailPerNight.setHotelierCurrencyCode(pricingDetail.getHotelierCurrencyCode());
				pricingDetailPerNight.setSubTitle(pricingDetail.getSubTitle());
				pricingDetailPerNight.setSubLine(pricingDetail.getSubLine());
				pricingDetails.add(pricingDetailPerNight);
				if (PRICE_AFTER_DISCOUNT_KEY.equalsIgnoreCase(pricingDetail.getKey())) {
					discountedPriceExist = true;
				}
			}
		}
		if (!discountedPriceExist) {
			copyBaseFareToPriceAfterDiscount(pricingDetails);
		}
		return pricingDetails;
	}

	private void copyBaseFareToPriceAfterDiscount(List<PricingDetails> pricingDetails) {
		PricingDetails baseFare = pricingDetails.stream().filter(pricingDetail -> BASE_FARE_KEY.equalsIgnoreCase(pricingDetail.getKey())).findFirst().orElse(null);
		if (baseFare != null) {
			PricingDetails priceAfterDiscount = new PricingDetails();
			BeanUtils.copyProperties(baseFare, priceAfterDiscount);
			priceAfterDiscount.setLabel(polyglotService.getTranslatedData(PRICE_AFTER_DISCOUNT_KEY));
			priceAfterDiscount.setKey(PRICE_AFTER_DISCOUNT_KEY);
			pricingDetails.add(priceAfterDiscount);
		}
	}

	private double getAmount(String key, double amount, int los, double preAppliedCouponAmount) {
		key = key.toUpperCase();
		if (preAppliedCouponAmount != 0.0) {
			if ("TOTAL_DISCOUNT".equalsIgnoreCase(key)) {
				return los > 0 ? Utility.round((amount + preAppliedCouponAmount) / los, 0) : 0.0;
			}
			if (Arrays.asList("PRICE_AFTER_DISCOUNT", "TOTAL_AMOUNT").contains(key)) {
				return los > 0 ? Utility.round((amount - preAppliedCouponAmount) / los, 0) : 0.0;
			}
		}
		return los > 0 ? Utility.round(amount / los, 0) : 0.0;
	}

	private String buildRoomDesc(RoomInfo roomInfo) {
		String roomDesc = "";
		if (StringUtils.isNotEmpty(roomInfo.getRoomSize())) {
			roomDesc = roomDesc + roomInfo.getRoomSize() + SPACE + roomInfo.getRoomSizeUnit();
		}
		if (CollectionUtils.isNotEmpty(roomInfo.getBeds()) && StringUtils.isNotBlank(roomInfo.getBeds().get(0).getType())) {
			roomDesc +=  PIPE_SEPARATOR + roomInfo.getBeds().get(0).getType();
		}
		if (StringUtils.isNotEmpty(roomInfo.getRoomViewName())) {
			roomDesc += PIPE_SEPARATOR + roomInfo.getRoomViewName();
		}
		return roomDesc;
	}

	public TagInfo prepareTagInfo(BlackBenefits blackBenefits) {
		if (blackBenefits != null && blackBenefits.isRoomUpgrade()) {
			return TagInfo.builder()
					.title(polyglotService.getTranslatedData(ConstantsTranslation.ROOM_UPGRADED_TAG_TITLE))
					.color("#007E7D")
					.build();
		}
		return null;
	}

	public void removePlaceHolderPersuasionsForSection(Hotel hotel, String sectionName) {

		Map<String, List<String>> placeholdersToShowSectionMap = null;
		try {
			placeholdersToShowSectionMap = objectMapperUtil.getObjectFromJsonWithType(placeHolderToShowSectionMap, new TypeReference<Map<String, List<String>>>() {},
					DependencyLayer.CLIENTGATEWAY);

			if(MapUtils.isNotEmpty(placeholdersToShowSectionMap) && placeholdersToShowSectionMap.containsKey(sectionName) && hotel.getHotelPersuasions()!=null) {
				List<String> placeHoldersToShow = placeholdersToShowSectionMap.get(sectionName);
				Map<Object, Object> hotelPersuasions = (Map<Object, Object>) hotel.getHotelPersuasions();
				Set<Object> existingPersuasionKeySet = new HashSet<>(hotelPersuasions.keySet());
				for (Object key : existingPersuasionKeySet) {
					if (!placeHoldersToShow.contains(key.toString())) {
						hotelPersuasions.remove(key.toString());
					}
				}
			}
		} catch(Exception e) {
			logger.error("Error in removing unwanted persuasions from hotelPersuasions ", e);
		}
	}

	public com.mmt.hotels.clientgateway.response.searchHotels.UpsellRateplanInfo buildUpsellRateplanInfo(UpsellRateplanInfo hesUpsellRateplanInfo, String currency){
		if(hesUpsellRateplanInfo != null){
			String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(currency) ? currency : DEFAULT_CUR_INR).getCurrencySymbol();
			com.mmt.hotels.clientgateway.response.searchHotels.UpsellRateplanInfo upsellRateplanInfo = new com.mmt.hotels.clientgateway.response.searchHotels.UpsellRateplanInfo();
			upsellRateplanInfo.setCTA(polyglotService.getTranslatedData(SEE_ALL_CTA_TITLE));
			upsellRateplanInfo.setBaseRoomCode(hesUpsellRateplanInfo.getBaseRoomCode());
			upsellRateplanInfo.setBaseRatePlanCode(hesUpsellRateplanInfo.getBaseRateplanCode());
			String titleText = polyglotService.getTranslatedData(NEXT_BEST_RATEPLAN_GCC_TITLE_TEXT);
			titleText = StringUtils.isNotEmpty(titleText) ? titleText.replace("{cur}", currencySymbol) : titleText;
			upsellRateplanInfo.setTitleText(hesUpsellRateplanInfo.getUpsellType() + MessageFormat.format(titleText,hesUpsellRateplanInfo.getUpsellRatePlanPriceDiff()));
			return upsellRateplanInfo;
		}
		return null;
	}

	public com.mmt.hotels.clientgateway.response.searchHotels.UpsellRateplanInfo buildUpsellRateplanInfo(com.gommt.hotels.orchestrator.model.response.listing.UpsellRatePlanInfo hesUpsellRateplanInfo, String currency) {
		if (hesUpsellRateplanInfo != null) {
			String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(currency) ? currency : DEFAULT_CUR_INR).getCurrencySymbol();
			com.mmt.hotels.clientgateway.response.searchHotels.UpsellRateplanInfo upsellRateplanInfo = new com.mmt.hotels.clientgateway.response.searchHotels.UpsellRateplanInfo();
			upsellRateplanInfo.setCTA(polyglotService.getTranslatedData(SEE_ALL_CTA_TITLE));
			upsellRateplanInfo.setBaseRoomCode(hesUpsellRateplanInfo.getBaseRoomCode());
			upsellRateplanInfo.setBaseRatePlanCode(hesUpsellRateplanInfo.getBaseRateplanCode());
			String titleText = polyglotService.getTranslatedData(NEXT_BEST_RATEPLAN_GCC_TITLE_TEXT);
			titleText = StringUtils.isNotEmpty(titleText) ? titleText.replace("{cur}", currencySymbol) : titleText;
			upsellRateplanInfo.setTitleText(hesUpsellRateplanInfo.getUpsellType() + MessageFormat.format(titleText, hesUpsellRateplanInfo.getUpsellRatePlanPriceDiff()));
			return upsellRateplanInfo;
		}
		return null;
	}

	public List<CardData> addBusinessIdentificationCard(int savingPerc) {
		List<CardData> cardsData = new ArrayList<>();
		if (CollectionUtils.isEmpty(businessIdentificationCards) || savingPerc == 0) {
			return null;
		}
		for (CardData cardData : businessIdentificationCards) {
			if (cardData == null) {
				continue;
			}
			CardData cardDataModified = new CardData();
			CardInfo cardInfo = cardData.getCardInfo();
			CardInfo cardInfoModified = new CardInfo();
			BeanUtils.copyProperties(cardInfo, cardInfoModified);
			if (cardInfoModified != null) {
				String subText = cardInfoModified.getSubText();
				String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
				if(utility.isReviewPageAPI(controller)){
					cardInfoModified.setSubText(cardInfoModified.getSubTextReview());
				}else{
					if (StringUtils.isNotEmpty(subText)) {
						cardInfoModified.setSubText(subText.replace(Constants.SAVING_PERC, String.valueOf(savingPerc)));
					}
				}
				cardInfoModified.setSubTextReview(null);
				cardDataModified.setCardInfo(cardInfoModified);
				cardsData.add(cardDataModified);
			}
		}
		return cardsData;
	}

	public List<CardData> buildBusinessIdentificationCards(HotelRates hotelRates, String affiliateId) {
		if(StringUtils.isEmpty(affiliateId) || hotelRates==null){
			return null;
		}
		try {
			String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
			Set<String> allowedSegments = new HashSet<>();
			if(utility.isDetailPageAPI(controller)){
				String segmentId = hotelRates.getLowestRate()!=null?hotelRates.getLowestRate().getSegmentId(): EMPTY_STRING;
				if(StringUtils.isNotEmpty(segmentId)){
					allowedSegments.add(segmentId);
				}
			}else if(utility.isReviewPageAPI(controller) && CollectionUtils.isNotEmpty(hotelRates.getAvailableSegments())){
				allowedSegments.addAll(hotelRates.getAvailableSegments());
			}
			if (utility.isBusinessIdentificationApplicable(affiliateId,allowedSegments)) {
				int savingPerc = 0;
				if (hotelRates != null) {
					if (hotelRates.getRoomTypeDetails() != null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())
							&& hotelRates.getRoomTypeDetails().getRoomType().entrySet().iterator().next() != null) {
						RoomType roomType = hotelRates.getRoomTypeDetails().getRoomType().entrySet().iterator().next().getValue();
						if (roomType != null && MapUtils.isNotEmpty(roomType.getRatePlanList()) && roomType.getRatePlanList().entrySet().iterator().next() != null) {
							com.mmt.hotels.model.response.pricing.RatePlan ratePlan = roomType.getRatePlanList().entrySet().iterator().next().getValue();
							if (ratePlan != null && ratePlan.getDisplayFare() != null && ratePlan.getDisplayFare().getDisplayPriceBreakDown() != null) {
								savingPerc = (int) ratePlan.getDisplayFare().getDisplayPriceBreakDown().getSavingPerc();
							}
						}
					} else if (hotelRates.getRecommendedRoomTypeDetails() != null && hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare() != null && hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() != null) {
						savingPerc = (int) hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getSavingPerc();
					} else if (hotelRates.getOccupencyLessRoomTypeDetails() != null && hotelRates.getOccupencyLessRoomTypeDetails().getTotalDisplayFare() != null && hotelRates.getOccupencyLessRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() != null) {
						savingPerc = (int) hotelRates.getOccupencyLessRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getSavingPerc();
					}
				}
				return addBusinessIdentificationCard(savingPerc);
			}
		} catch (Exception ex) {
			logger.error("Error while building business identification cards {}", ex.getMessage());
		}
		return null;
	}

	public List<CardData> buildBusinessIdentificationCardsNonRegisteredUser() {
		List<CardData> cardsData = new ArrayList<>();
		String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
		if(CollectionUtils.isEmpty(businessIdentificationNonRegisteredUserCard) || !utility.isDetailPageAPI(controller)) {
			return null;
		}
		for (CardData cardData : businessIdentificationNonRegisteredUserCard) {
			if (cardData == null) {
				continue;
			}
			CardData cardDataModified = new CardData();
			CardInfo cardInfo = cardData.getCardInfo();
			CardInfo cardInfoModified = new CardInfo();
			BeanUtils.copyProperties(cardInfo, cardInfoModified);
			cardDataModified.setCardInfo(cardInfoModified);
			cardDataModified.setSequence(cardData.getSequence());
			cardsData.add(cardDataModified);
		}
		return cardsData;
	}

	public void buildAltDatesPersuasionAndBottomsheet(List<AlternatePriceCard> alternatePriceCards, SearchRoomsRequest searchRoomsRequest,
													  String expData, SearchRoomsResponse searchRoomsResponse, SearchCriteria searchRoomsCriteria,
													  int los, boolean isAltAccoHotel, boolean isHighSellingAltAcco) {
		AltDatesBottomsheet altDatesBottomsheet = null;
		PersuasionObject altDatesPersuasion = null;
		Integer roomCount = 1;
		boolean isGroupBooking = false;
		try {
			logger.info("GCC AlternateDates : Before filtering alternatePriceCards: {}, HotelId: {}", alternatePriceCards, searchRoomsRequest.getSearchCriteria().getHotelId());
			alternatePriceCards = getFilteredData(alternatePriceCards);
			if(alternatePriceCards == null || alternatePriceCards.isEmpty()
					|| searchRoomsRequest.getFeatureFlags() == null || StringUtils.isBlank(searchRoomsRequest.getFeatureFlags().getAltCheaperDates())) {
				logger.info("GCC AlternateDates : Received null alternatePriceCards from HES: {}, HotelId: {}", alternatePriceCards, searchRoomsRequest.getSearchCriteria().getHotelId());
				return;
			}
			logger.info("GCC AlternateDates : Filtered alternatePriceCards: {}, HotelId: {}", alternatePriceCards, searchRoomsRequest.getSearchCriteria().getHotelId());
			if(null != searchRoomsRequest.getFeatureFlags().getAltCheaperDates()) {
				if (searchRoomsRequest.getFeatureFlags().getAltCheaperDates().contains("persuasion")) {
					altDatesPersuasion = buildAltDatesPersuasion(getCheaperData(alternatePriceCards));
					logger.debug("GCC AlternateDates : altDatesPersuasion: {}, HotelId: {}", altDatesPersuasion, searchRoomsRequest.getSearchCriteria().getHotelId());
				}
				if (searchRoomsRequest.getFeatureFlags().getAltCheaperDates().contains("bottomsheet")) {
					if (CollectionUtils.isNotEmpty(searchRoomsCriteria.getRoomStayCandidates()) && searchRoomsCriteria.getRoomStayCandidates().get(0).getRooms() != 0) {
						roomCount = searchRoomsCriteria.getRoomStayCandidates().get(0).getRooms();
					}
					if(MapUtils.isNotEmpty(searchRoomsRequest.getExpDataMap()) && searchRoomsRequest.getExpDataMap().containsKey(BEDROOM_COUNT_AVAILABLE) &&
							searchRoomsCriteria.getRoomStayCandidates().get(0).getRooms() == null) {
						roomCount=1;
					}
					if (searchRoomsRequest.getRequestDetails() != null) {
						isGroupBooking = Utility.isGroupBookingFunnel(searchRoomsRequest.getRequestDetails().getFunnelSource());
					}
					String priceDisplayMessage = getPriceDisplayMessage(expData, roomCount, searchRoomsResponse.getPropertySellableType(),
							los, isGroupBooking, isAltAccoHotel, isHighSellingAltAcco);
					altDatesBottomsheet = buildAltDatesBottomsheet(alternatePriceCards, priceDisplayMessage);
					logger.debug("GCC AlternateDates : buildAltDatesBottomsheet: {}, HotelId: {}", altDatesBottomsheet, searchRoomsRequest.getSearchCriteria().getHotelId());
				}
			}
			searchRoomsResponse.setAltDatesBottomsheet(altDatesBottomsheet);
			if(altDatesPersuasion != null) {
				if(CollectionUtils.isEmpty(searchRoomsResponse.getPersuasions())) {
					searchRoomsResponse.setPersuasions(new ArrayList<>());
				}
				searchRoomsResponse.getPersuasions().add(altDatesPersuasion);
			}
		} catch (Exception e) {
			logger.error("GCC AlternateDates : Error while building bottomsheet and persuasions. Exception = {}",e.getMessage());
			e.printStackTrace();
		}
	}

	private AlternatePriceCard getCheaperData(List<AlternatePriceCard> alternatePriceCards) {
		return alternatePriceCards.stream()
				.min(Comparator.comparingDouble(AlternatePriceCard::getPrice))
				.orElse(null);
	}

	private List<AlternatePriceCard> getFilteredData(List<AlternatePriceCard> alternatePriceCards) {
		if (alternatePriceCards == null || alternatePriceCards.isEmpty()) {
			return Collections.emptyList();
		}
		return alternatePriceCards.stream()
				.filter(card -> card.isCheaper() && !card.isSelected() && card.getDelta() >= deltaPercentageForAltDates * card.getPrice() / 100)
				.sorted(Comparator.comparingDouble(AlternatePriceCard::getPrice))
				.limit(2)
				.collect(Collectors.toList());
	}

	private AltDatesBottomsheet buildAltDatesBottomsheet(List<AlternatePriceCard> alternatePriceCards, String displayText) {
		AltDatesBottomsheet altDatesBottomsheet = new AltDatesBottomsheet();
		altDatesBottomsheet.setTitle(polyglotService.getTranslatedData(ALT_DATE_BOTTOMSHEET_TITLE)
				.replace("{currency}", alternatePriceCards.get(0).getCurrency())
				.replace("{delta}", String.valueOf((int) alternatePriceCards.get(0).getDelta()))
		);
		altDatesBottomsheet.setLogoUrl(ALT_DATE_BOTTOMSHEET_LOGO_URL);
		altDatesBottomsheet.setData(buildAltDatesBottomsheetData(alternatePriceCards, displayText));
		return altDatesBottomsheet;
	}

	private PersuasionObject buildAltDatesPersuasion(AlternatePriceCard altDatesPriceCard) {
		PersuasionObject persuasionObject = new PersuasionObject();
		if(altDatesPriceCard==null){
			return null;
		}
		persuasionObject.setPlaceholder(ALT_DATE_PERSUASION_PLACEHOLDER);
		persuasionObject.setTemplate(IMAGE_TEXT_H);
		persuasionObject.setData(buildAltDatePersuasionData(altDatesPriceCard));
		persuasionObject.setStyle(buildAltDatePersuasionStyle());
		persuasionObject.setExtraDetails(buildAltDatePersuasionExtraDetail(altDatesPriceCard));
		return persuasionObject;
	}

	private PersuasionStyle buildAltDatePersuasionStyle() {
		PersuasionStyle persuasionStyle = new PersuasionStyle();
		persuasionStyle.setBgColor("#eaf5ff");
		persuasionStyle.setCornerRadii("8");
		persuasionStyle.setTextColor("#4a4a4a");
		return persuasionStyle;
	}

	private ExtraDetails buildAltDatePersuasionExtraDetail(AlternatePriceCard altDatesPriceCard) {
		ExtraDetails extraDetails = new ExtraDetails();
		extraDetails.setActionType("UPDATE_DATES");
		extraDetails.setCheckIn(altDatesPriceCard.getCheckIn());
		extraDetails.setCheckOut(altDatesPriceCard.getCheckOut());
		return extraDetails;
	}

	private List<PersuasionData> buildAltDatePersuasionData(AlternatePriceCard altDatesPriceCard) {
		List<PersuasionData> persuasionDataList = new ArrayList<>();
		PersuasionData persuasionData = new PersuasionData();
		persuasionData.setPersuasionType("ALT_DATES");
		persuasionData.setText(polyglotService.getTranslatedData(ALT_DATE_PERSUASIONS_DATA_TEXT)
				.replace("{currency}", altDatesPriceCard.getCurrency())
				.replace("{delta}", String.valueOf((int) altDatesPriceCard.getDelta()))
				.replace("{price}", String.valueOf((int) altDatesPriceCard.getPrice()))
				.replace("{dateRange}", formatDateForAltDates(altDatesPriceCard)));
		persuasionData.setStyle(buildAltDatePersuasionDataStyle());
		persuasionData.setHtml(Boolean.TRUE);
		persuasionDataList.add(persuasionData);
		return persuasionDataList;
	}

	private PersuasionStyle buildAltDatePersuasionDataStyle() {
		PersuasionStyle persuasionStyle = new PersuasionStyle();
		persuasionStyle.setHorizontalSpace(8);
		persuasionStyle.setVerticalSpace(8);
		return persuasionStyle;
	}

	private List<PriceCardDetail> buildAltDatesBottomsheetData(List<AlternatePriceCard> alternatePriceCards, String displayText) {
		List<PriceCardDetail> priceCardDetails = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(alternatePriceCards)) {
			for (AlternatePriceCard alternatePrice : alternatePriceCards) {
				PriceCardDetail priceCardDetail = new PriceCardDetail();
				priceCardDetail.setDateRange(fetchDateRangeForAltDates(alternatePrice));
				priceCardDetail.setText(polyglotService.getTranslatedData(ALT_DATE_BOTTOMSHEET_DATA_TEXT).replace("{dateRange}", formatDateForAltDates(alternatePrice)));
				priceCardDetail.setPriceSuffix(StringUtils.isEmpty(displayText) ? "per night" : displayText);
				priceCardDetail.setSubTextData(buildSubTextForAltDates(alternatePrice));
				priceCardDetail.setCurrency(alternatePrice.getCurrency());
				priceCardDetail.setPrice(alternatePrice.getPrice());
				priceCardDetails.add(priceCardDetail);
			}
		}
		return priceCardDetails;
	}

	private SubTextData buildSubTextForAltDates(AlternatePriceCard alternatePrice) {
		SubTextData subTextData = new SubTextData();
		subTextData.setText(polyglotService.getTranslatedData(ALT_DATE_BOTTOMSHEET_DATA_SUB_TEXT)
				.replace("{currency}", alternatePrice.getCurrency())
				.replace("{delta}", String.valueOf((int) alternatePrice.getDelta())));
		return subTextData;
	}

	private DateRange fetchDateRangeForAltDates(AlternatePriceCard alternatePriceCard) {
		DateRange dateRange = new DateRange();
		dateRange.setCheckIn(alternatePriceCard.getCheckIn());
		dateRange.setCheckOut(alternatePriceCard.getCheckOut());
		return dateRange;
	}

	public static String formatDateForAltDates(AlternatePriceCard alternatePriceCard) {
		SimpleDateFormat originalFormat = new SimpleDateFormat("yyyy-MM-dd");
		SimpleDateFormat newFormat = new SimpleDateFormat("dd MMM");
		try {
			// Parse the original date string to a Date object
			Date checkInDate = originalFormat.parse(alternatePriceCard.getCheckIn());
			Date checkOutDate = originalFormat.parse(alternatePriceCard.getCheckOut());
			// Format the Date object to the new format
			return newFormat.format(checkInDate) + " - " + newFormat.format(checkOutDate);
		} catch (ParseException e) {
			logger.error("GCC AlternateDates : Error while converting date format. AlternatePriceCard = {}",alternatePriceCard);
			return null; // Or handle the error as needed
		}
	}

	public CardPayloadData buildForexAndCabCardPayload(Map<String, String> expDataMap, String cityCode, String cabsDeepLinkUrl, boolean bookingDeviceDesktop, String pageContext) {
		CardPayloadData cardPayloadData = new CardPayloadData();
		cardPayloadData.setGenericCardData(buildForexAndCabCardGenericData(expDataMap, cityCode, cabsDeepLinkUrl, bookingDeviceDesktop, pageContext));
		return cardPayloadData;
	}

	private List<GenericCardPayloadDataCG> buildForexAndCabCardGenericData(Map<String, String> expDataMap, String cityCode, String cabsDeepLinkUrl, boolean bookingDeviceDesktop, String pageContext) {
		List<GenericCardPayloadDataCG> forexAndCabCardsData = new ArrayList<>();
		if(utility.isExperimentOn(expDataMap, ExperimentKeys.forexCard.getKey())) {
			forexAndCabCardsData.add(buildForexCard(bookingDeviceDesktop, pageContext));
		}
		if(utility.isExperimentOn(expDataMap, ExperimentKeys.cabCard.getKey()) && null!=cabCardCityList && cabCardCityList.contains(cityCode)) {
			forexAndCabCardsData.add(buildCabCard(cabsDeepLinkUrl, bookingDeviceDesktop, pageContext));
		}
		return forexAndCabCardsData;
	}

	private GenericCardPayloadDataCG buildCabCard(String cabDeepLink, boolean bookingDeviceDesktop, String pageContext) {
		GenericCardPayloadDataCG cabCard = new GenericCardPayloadDataCG();
		cabCard.setId(CAB_CARD_ID);
		cabCard.setTitleText(polyglotService.getTranslatedData(CAB_CARD_TITLE).replace("{CURRENCY_SYMBOL}", HINDI_RUPEE));
		cabCard.setSubText(polyglotService.getTranslatedData(CAB_CARD_SUBTEXT));
		cabCard.setIconUrl(cabCashbackIconUrl);
		cabCard.setCardAction(buildCardActionForCabCard(cabDeepLink,bookingDeviceDesktop, pageContext));
		return cabCard;
	}

	private GenericCardPayloadDataCG buildForexCard(boolean bookingDeviceDesktop, String pageContext) {
		GenericCardPayloadDataCG forexCard = new GenericCardPayloadDataCG();
		forexCard.setId(FOREX_CARD_ID);
		forexCard.setTitleText(polyglotService.getTranslatedData(FOREX_CARD_TITLE).replace("{CURRENCY_SYMBOL}", HINDI_RUPEE));
		forexCard.setSubText(polyglotService.getTranslatedData(FOREX_CARD_SUBTEXT));
		forexCard.setIconUrl(forexCashbackIconUrl);
		forexCard.setCardAction(buildCardActionForForexCard(bookingDeviceDesktop, pageContext));
		return forexCard;
	}

	private List<CardAction> buildCardActionForForexCard(boolean bookingDeviceDesktop, String pageContext) {
		List<CardAction> cardActions = new ArrayList<>();
		if(PAGE_CONTEXT_REVIEW.equalsIgnoreCase(pageContext) || bookingDeviceDesktop) {
			CardAction cardActionForexMore = new CardAction();
			cardActionForexMore.setTitle(polyglotService.getTranslatedData(FOREX_CAB_CARD_CARDACTION_MORE));
			cardActionForexMore.setWebViewUrl(forexWebViewUrl);
			cardActionForexMore.setType("MORE");
			cardActions.add(cardActionForexMore);
		}
		if(PAGE_CONTEXT_THANK_YOU.equalsIgnoreCase(pageContext)){
			CardAction cardActionForexClaim = new CardAction();
			cardActionForexClaim.setTitle(polyglotService.getTranslatedData(FOREX_CAB_CARD_CARDACTION_CLAIM));
			cardActionForexClaim.setDeeplinkUrl(getDeeplinkUrl(forexDeeplinkUrlApps, forexDeeplinkUrlDtPwa));
			cardActionForexClaim.setType("CLAIM");
			cardActions.add(cardActionForexClaim);
		}
		return cardActions;
	}

	private List<CardAction> buildCardActionForCabCard(String cabsDeepLinkUrl, boolean bookingDeviceDesktop, String pageContext) {
		List<CardAction> cardActions = new ArrayList<>();

		if(PAGE_CONTEXT_REVIEW.equalsIgnoreCase(pageContext) || bookingDeviceDesktop) {
			CardAction cardActionCabMore = new CardAction();
			cardActionCabMore.setTitle(polyglotService.getTranslatedData(FOREX_CAB_CARD_CARDACTION_MORE));
			cardActionCabMore.setWebViewUrl(cabWebViewUrl);
			cardActionCabMore.setType("MORE");
			cardActions.add(cardActionCabMore);
		}
		if(PAGE_CONTEXT_THANK_YOU.equalsIgnoreCase(pageContext)) {
			String cabDeepLink = StringUtils.isNotEmpty(cabsDeepLinkUrl) ? cabsDeepLinkUrl : getDeeplinkUrl(cabsforexDeeplinkUrlApps, cabsforexDeeplinkUrlDtPwa);
			CardAction cardActionCabClaim = new CardAction();
			cardActionCabClaim.setTitle(polyglotService.getTranslatedData(FOREX_CAB_CARD_CARDACTION_CLAIM));
			cardActionCabClaim.setDeeplinkUrl(cabDeepLink);
			cardActionCabClaim.setType("CLAIM");
			cardActions.add(cardActionCabClaim);
		}
		return cardActions;
	}

	private String getDeeplinkUrl(String appUrl, String webUrl) {
		String clientType = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
		if (ANDROID.equalsIgnoreCase(clientType) || DEVICE_IOS.equalsIgnoreCase(clientType)) {
			return appUrl;
		} else {
			return webUrl;
		}
	}

	public Map<String, String> buildTrackingMap(Map<String, String> trackingMap) {
		Map<String, String> resultTrackingMap = new HashMap<>();
		if (trackingMap == null || MapUtils.isEmpty(trackingMap))
			return resultTrackingMap;
		for (Map.Entry<String, String> entry : trackingMap.entrySet()) {
			String key = entry.getKey();
			String value = entry.getValue();
			if (resultTrackingMap.size() <= TRACKING_MAP_THRESHOLD) {
				if (StringUtils.isNotEmpty(value)) {
					resultTrackingMap.put(key, value);
				}
			}
		}
		return resultTrackingMap;
	}

	public String getEvarBasedOnCountryAndRegion(String userCountry, String hotelCountry) {
		if ("in".equalsIgnoreCase(userCountry) && "in".equalsIgnoreCase(hotelCountry)) {
			return "in";
		} else if ("ae".equalsIgnoreCase(userCountry)) {
			return "uae";
		} else if ("sa".equalsIgnoreCase(userCountry) || "qa".equalsIgnoreCase(userCountry) || "bh".equalsIgnoreCase(userCountry) || "kw".equalsIgnoreCase(userCountry) || "om".equalsIgnoreCase(userCountry)) {
			return "gcc";
		} else if (!"sa".equalsIgnoreCase(userCountry) && !"ae".equalsIgnoreCase(userCountry) && !"qa".equalsIgnoreCase(userCountry) && !"bh".equalsIgnoreCase(userCountry) && !"kw".equalsIgnoreCase(userCountry) && !"om".equalsIgnoreCase(userCountry) && ("uni".equalsIgnoreCase(hotelCountry) || "sau".equalsIgnoreCase(hotelCountry) || "qat".equalsIgnoreCase(hotelCountry) || "bhr".equalsIgnoreCase(hotelCountry) || "kwt".equalsIgnoreCase(hotelCountry) || "oma".equalsIgnoreCase(hotelCountry) || "ae".equalsIgnoreCase(hotelCountry))) {
			return "gcc-io";
		} else if (!"in".equalsIgnoreCase(userCountry) && !"sa".equalsIgnoreCase(userCountry) && !"ae".equalsIgnoreCase(userCountry) && !"qa".equalsIgnoreCase(userCountry) && !"bh".equalsIgnoreCase(userCountry) && !"kw".equalsIgnoreCase(userCountry) && !"om".equalsIgnoreCase(userCountry) && "in".equalsIgnoreCase(hotelCountry)) {
			return "in-io";
		} else if (!"sa".equalsIgnoreCase(userCountry) && !"ae".equalsIgnoreCase(userCountry) && !"qa".equalsIgnoreCase(userCountry) && !"bh".equalsIgnoreCase(userCountry) && !"kw".equalsIgnoreCase(userCountry) && !"om".equalsIgnoreCase(userCountry) && !"in".equalsIgnoreCase(hotelCountry) && !"uni".equalsIgnoreCase(hotelCountry) && !"sau".equalsIgnoreCase(hotelCountry) && !"qat".equalsIgnoreCase(hotelCountry) && !"bhr".equalsIgnoreCase(hotelCountry) && !"kwt".equalsIgnoreCase(hotelCountry) && !"oma".equalsIgnoreCase(hotelCountry) && !"ae".equalsIgnoreCase(hotelCountry)) {
			return "a2a";
		}
		return EMPTY_STRING;
	}


	public String buildPixelUrl(PixelUrlConfig data) {

		if(data == null){
			return null;
		}

		StringBuilder pixelUrl = new StringBuilder(pixelBaseUrl);

		if (StringUtils.isNotEmpty(data.getCurrency())) {
			pixelUrl.append("u6=").append(data.getCurrency()).append(";");
			pixelUrl.append("u19=").append(data.getCurrency()).append(";");
		}

		if (StringUtils.isNotEmpty(data.getLanguage())) {
			pixelUrl.append("u7=").append(data.getLanguage()).append(";");
		}

		if (StringUtils.isNotEmpty(data.getCityName())) {
			try {
				pixelUrl.append("u20=").append(URLEncoder.encode(data.getCityName(), "UTF-8").replace("+", "%20")).append(SEMICOLON);
			} catch (Exception e) {
				logger.error("error while encoding city name: ", e);
			}
		}

		if (StringUtils.isNotEmpty(data.getHotelName())) {
			try {
				pixelUrl.append("u21=").append(URLEncoder.encode(data.getHotelName(), "UTF-8").replace("+", "%20")).append(SEMICOLON);
			} catch (Exception e) {
				logger.error("error while encoding hotel name: ", e);
			}
		}

		if (data.getCheckIn() != null) {
			pixelUrl.append("u22=").append(Utility.formatDateForPixelUrl(data.getCheckIn())).append(";");
		}

		if (data.getCheckOut() != null) {
			pixelUrl.append("u23=").append(Utility.formatDateForPixelUrl(data.getCheckOut())).append(";");
		}

		if (data.getGuestCount() != 0 && data.getGuestCount() > 0) {
			pixelUrl.append("u24=").append(data.getGuestCount()).append(";");
		}

		if (StringUtils.isNotEmpty(data.getUserCountry())) {
			pixelUrl.append("u30=").append(data.getUserCountry()).append(";");
		}

		if (StringUtils.isNotEmpty(data.getDeviceId())) {
			pixelUrl.append("dc_rdid=").append(data.getDeviceId()).append(";");
		}

		if (StringUtils.isNotEmpty(data.getJourneyId())) {
			pixelUrl.append("ord=").append(data.getJourneyId());
		}

		return pixelUrl.toString();
	}
}

